# 插件管理网关维度规则修复测试

## 修复内容总结

### 1. 固定规则名称
- 修改了 `InstallPluginModal.tsx`、`InstalledPlugins.tsx` 和 `index.tsx` 中的默认配置
- 将动态生成的规则名称改为固定的 `ext-auth-config`

### 2. 网关维度规则逻辑
在 `PluginConfigDrawer.tsx` 中实现了以下逻辑：

#### 2.1 检查现有规则
- 添加了 `checkExistingGatewayRule()` 函数
- 在打开抽屉时自动检查是否存在 `ext-auth-config` 规则

#### 2.2 配置回写逻辑
- 如果存在 `ext-auth-config` 规则：回写现有规则内容
- 如果不存在：使用默认配置参数

#### 2.3 创建/更新逻辑
- 点击确定时，先检查规则是否存在
- 存在：调用更新接口 (`updateExternalAuthPlugin`)
- 不存在：调用创建接口 (`createExternalAuthPlugin`)

## 测试步骤

### 场景1：首次安装配置（无现有规则）
1. 点击"安装并配置"按钮
2. 验证默认选中网关维度
3. 验证回写默认配置参数
4. 修改配置后点击确定
5. 验证调用创建接口，规则名称为 `ext-auth-config`

### 场景2：已有规则的配置（有现有规则）
1. 点击"规则配置"按钮
2. 验证默认选中网关维度
3. 验证回写现有 `ext-auth-config` 规则内容
4. 修改配置后点击确定
5. 验证调用更新接口

## 关键修改点

### 文件：InstallPluginModal.tsx
```javascript
// 修改前
name: `${plugin.pluginName}-${Date.now()}`,

// 修改后
name: 'ext-auth-config', // 固定名称
```

### 文件：PluginConfigDrawer.tsx
```javascript
// 新增检查函数
const checkExistingGatewayRule = async () => {
  // 检查是否存在ext-auth-config规则
}

// 修改确定按钮逻辑
const handleConfirm = async () => {
  // 检查规则是否存在，决定调用创建还是更新接口
}
```

## 修复的错误处理问题

### 问题描述
第一次安装时报错"外部认证插件不存在"

### 修复方案
1. **分离规则检查逻辑** - 将规则存在性检查从创建/更新逻辑中分离
2. **改进错误处理** - 正确处理API返回的错误信息
3. **优化检查流程** - 先检查规则是否存在，再决定调用哪个接口

### 修复后的逻辑
```javascript
// 1. 先检查规则是否存在
let ruleExists = false;
try {
  const checkResponse = await getExternalAuthPluginDetail(instanceId, 'ext-auth-config');
  if (checkResponse?.success && checkResponse?.result) {
    ruleExists = true;
  }
} catch (error) {
  ruleExists = false; // 规则不存在
}

// 2. 根据检查结果调用对应接口
if (ruleExists) {
  // 调用更新接口
  response = await updateExternalAuthPlugin(instanceId, 'ext-auth-config', data);
} else {
  // 调用创建接口
  response = await createExternalAuthPlugin(instanceId, data);
}
```

## 预期结果
1. 规则名称固定为 `ext-auth-config`
2. 网关维度默认选中
3. 正确回写配置（现有规则或默认配置）
4. 正确调用创建/更新接口
5. **第一次安装不再报错** ✅
