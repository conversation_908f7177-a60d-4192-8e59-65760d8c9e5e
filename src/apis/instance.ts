import {BaseResponseType, request} from '@baidu/bce-react-toolkit';

import {BASE_URL} from './index';

/**
 * 获取已注册的实例列表
 * 
 * 返回字段包括：
 * - instanceId：实例ID
 * - name：实例名称
 * - ingressStatus：实例状态
 * - replicas：实例节点数量
 * - vpcCidr：VPC网段
 * - vpcId：VPC ID
 * - subnetId：子网ID
 * - gatewayType：网关规格
 * - internalIP：私网接入地址
 * - publicAccessible：是否可公网访问
 * - externalIP：公网接入地址
 * - description：描述信息
 * - createTime：创建时间
 * - deleteProtection：是否开启删除保护
 * - region：所属地域
 * - namespace：命名空间
 */
export function getInstanceList(
  data?: {
    pageNo?: number;
    pageSize?: number;
    orderBy?: string;
    order?: string;
    keyword?: string;
  },
  region?: string,
  silent?: boolean
): BaseResponseType<any> {
  console.log('调用查询实例列表接口，参数：', data, '地域：', region);
  return request({
    url: `/api/aigw/v1/aigateway/list`,
    params: data,
    silent,
    headers: {
      'X-Region': region || ''
    }
  });
}

/**
 * 删除网关实例
 * 
 * 请求参数：
 * - instanceId：实例ID
 * - region：地域信息，会作为X-Region请求头传递
 */
export function deleteRegistrationInstance(
  instanceId: string,
  region?: string
): BaseResponseType<any> {
  console.log('调用删除实例接口，实例ID：', instanceId, '地域：', region);
  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}`,
    method: 'DELETE',
    headers: {
      'X-Region': region || ''
    }
  });
}

/**
 * 获取VPC网络列表
 * @param data 请求参数
 * @param region 地域，通过X-Region请求头传递
 */
export function getVpcNetworkList(data?: any, region?: string): BaseResponseType<any> {
  // 创建一个新的参数对象，排除region字段
  const params = {...data};
  if (params.region) {
    delete params.region;
  }
  
  return request({
    url: `/api/aigw/v1/aigateway/vpc/vpcList`,
    params,
    headers: {
      'X-Region': region || (data?.region ? data.region : '')
    }
  });
}

/**
 * 获取子网列表
 * @param data 请求参数，必须包含vpcId
 * @param region 地域，通过X-Region请求头传递
 */
export function getSubnetList(data?: any, region?: string): BaseResponseType<any> {
  // 创建一个新的参数对象，排除region字段
  const params = {...data};
  if (params.region) {
    delete params.region;
  }
  
  return request({
    url: `/api/aigw/v1/aigateway/vpc/${data.vpcId}/subnetList`,
    headers: {
      'X-Region': region || (data?.region ? data.region : '')
    }
  });
}

/**
 * 获取子网可用ip数，这里没有封装，需要注意
 */
export function getSubNetIpNumber(subnetId?: string): BaseResponseType<any> {
  return request({
    url: `/api/network/v1/subnet/${subnetId}`
  });
}

/**
 * 获取企业安全组列表
 */
export function getEsgInstanceList(data?: any): BaseResponseType<any> {
  return request({
    url: `/api/network/v1/enterprise/security/list`,
    data,
    method: 'POST'
  });
}

/**
 * 获取cprom实例列表
 */
export function getPrometheusList(data?: any): BaseResponseType<any> {
  return request({
    url: `/api/cprom/service/v1/instances`,
    params: data
  });
}

/**
 * 获取cprom实例token
 */
export function getPrometheusTokenList(
  cpromInstanceId?: any
): BaseResponseType<any> {
  return request({
    url: `/api/cprom/service/v1/instance/${cpromInstanceId}/token`
  });
}

/**
 * 创建网关实例
 *
 * 请求参数：
 * - name: 实例名称，必须
 * - vpcId: 私有网络ID，必须
 * - vpcCidr: VPC网段，必须
 * - subnetId: 子网ID，必须
 * - gatewayType: 网关规格，必须，当前仅支持small
 * - isInternal: 是否仅内网访问，字符串，可选，"true"或"false"，默认为"true"
 * - replicas: 节点数量，可选，范围[1,5]，默认为2
 * - description: 网关实例描述，可选，最多64个字符
 * - deleteProtection: 网关删除保护，可选，默认为true
 * - clusters: 关联的集群列表，可选
 */
export function createRegistrationInstance(data?: any): BaseResponseType<any> {
  // 获取X-Region头部值
  const headerRegion = data?.region || 'bj';
  
  // 构造请求参数的深拷贝，并移除region字段
  const requestData = {...data};
  delete requestData.region;
  
  // 添加调试日志
  console.log('创建实例接口请求数据:', requestData);
  
  return request({
    url: `/api/aigw/v1/aigateway`,
    data: requestData,
    method: 'POST',
    headers: {
      'X-Region': headerRegion,
      'Content-Type': 'application/json'
    }
  });
}

/**
 * 获取实例详情信息
 */
export function getRegistrationInstance(
  registrationInstanceId?: string,
  region?: string
): BaseResponseType<any> {
  return request({
    url: `/api/aigw/v1/aigateway/${registrationInstanceId}`,
    headers: {
      'X-Region': region || ''
    }
  });
}

/**
 * 查询网关扩缩容状态
 *
 * 返回字段包括：
 * - ready：当前就绪的副本数
 * - upToDate：已更新到最新版本的副本数
 * - available：当前可用的副本数
 * - replicas：期望的副本数（总副本数）
 */
export function getGatewayStatus(
  instanceId: string,
  region?: string
): BaseResponseType<any> {
  console.log('调用查询网关扩缩容状态接口，实例ID：', instanceId, '地域：', region);
  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/gateway/status`,
    headers: {
      'X-Region': region || ''
    }
  });
}

/**
 * 修改实例访问策略
 */
export function putRegistrationInstance(data?: any): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${data.registrationInstanceId}`,
    data,
    method: 'PUT'
  });
}

/**
 * 获取命名空间列表
 */
export function getRegistrationNamespace(data?: any): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${data.instanceId}/namespaces`,
    params: data
  });
}

/**
 * 创建命名空间
 */
export function createRegistrationNamespace(data?: any): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${data.instanceId}/namespaces`,
    data,
    method: 'POST'
  });
}

/**
 * 删除命名空间
 */
export function deleteRegistrationNamespace(data?: any): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${data.instanceId}/namespaces`,
    params: data,
    method: 'DELETE'
  });
}

/**
 * 编辑命名空间
 */
export function putRegistrationNamespace(data?: any): BaseResponseType<any> {
  return request({
    url: `${BASE_URL}/registry/${data.instanceId}/namespaces`,
    data,
    method: 'PUT'
  });
}

/**
 * 获取CCE集群列表
 * 
 * 请求参数：
 * - region: 地域信息，会作为X-Region请求头传递
 * - vpcId: 可选，VPC ID，用于筛选特定VPC下的集群
 * 
 * 返回字段：
 * - clusterName: 集群名称
 * - clusterId: 集群ID
 * - clusterVersion: 集群版本
 * - admin: 是否为管理员
 * - istioInstalledStatus: 是否安装Istio
 */
export function getCCEClusterList(data?: {
  region: string;
  vpcId?: string;
}): BaseResponseType<any> {
  const params: any = {};
  if (data?.vpcId) {
    params.vpcId = data.vpcId;
  }
  
  return request({
    url: '/api/aigw/v1/aigateway/instance/clusterList',
    params,
    headers: {
      'X-Region': data?.region || ''
    }
  });
}

/**
 * 获取CCE集群详情
 * 
 * 根据文档：https://cloud.baidu.com/doc/CCE/s/pkgajvx7b#%E9%9B%86%E7%BE%A4%E8%AF%A6%E6%83%85
 * 
 * 请求参数：
 * - clusterId：集群ID
 */
export function getCCEClusterDetail(clusterId: string): BaseResponseType<any> {
  return request({
    url: `/api/cce/v2/cluster/${clusterId}`
  });
}

// 定义分页响应类型
interface PageData<T> {
  orderBy: string;
  order: string;
  pageNo: number;
  pageSize: number;
  totalCount: number;
  result: T[];
}

// 定义集群项类型
interface ClusterItem {
  clusterId: string;
  clusterName: string;
  status: string;
  remark: string;
  relationTime: string;
  updateTime: string;
}

/**
 * 获取实例关联的集群列表
 */
export function getInstanceRelatedClusters(
  instanceId: string,
  params?: {
    pageNo?: number,
    pageSize?: number,
    orderBy?: string,
    order?: string
  },
  region?: string
): BaseResponseType<any> {
  return request({
    url: `/api/aigw/v1/aigateway/instance/${instanceId}/clusterList`,
    params,
    headers: {
      'X-Region': region || ''
    }
  });
}

// 定义同VPC中的CCE集群项类型
interface VpcClusterItem {
  clusterId: string;
  clusterName: string;
  status: string;
  vpcCidr: string;
  region: string;
}

/**
 * 查询同VPC中的CCE集群
 */
export function getVpcClusters(vpcId: string, region?: string): BaseResponseType<VpcClusterItem[]> {
  return request({
    url: `/api/aigw/v1/aigateway/cluster/clusterList`,
    params: { vpcId },
    headers: {
      'X-Region': region || ''
    }
  });
}

/**
 * 关联集群
 */
export function associateCluster(
  instanceId: string,
  data: {
    clusters: Array<{ clusterId: string, clusterName: string }>,
    remark?: string,
    ingressSettings?: {
      enableIngress: boolean,
      enableAllIngressClasses: boolean,
      enableAllNamespaces: boolean,
      ingressClasses: string[],
      watchNamespaces: string[]
    }
  },
  region?: string
): BaseResponseType<{taskId: string, instanceId: string}> {
  return request({
    url: `/api/aigw/v1/aigateway/instance/${instanceId}/clusterList`,
    method: 'POST',
    data,
    headers: {
      'X-Region': region || ''
    }
  });
}

/**
 * 移除集群
 *
 * 从AI网关实例中移除关联的集群
 *
 * 请求参数：
 * - instanceId: 网关实例ID
 * - clusterId: 集群ID
 * - region: 地域
 *
 * 响应字段：
 * - result: null (移除集群成功后返回null)
 */
export function removeCluster(
  instanceId: string,
  clusterId: string,
  region?: string
): BaseResponseType<any> {
  return request({
    url: `/api/aigw/v1/aigateway/cluster/${instanceId}/${clusterId}`,
    method: 'DELETE',
    headers: {
      'X-Region': region || ''
    }
  });
}

/**
 * 更新实例信息
 * 
 * 请求参数：
 * - instanceId: 实例ID
 * - name: 实例名称（可选）
 * - description: 实例描述（可选）
 * - deleteProtection: 是否开启删除保护（可选）
 * - publicAccessible: 是否允许公网访问（可选）
 */
export function updateInstance(
  instanceId: string,
  data: {
    name?: string;
    description?: string;
    deleteProtection?: boolean;
    publicAccessible?: boolean;
    replicas?: number;
  },
  region?: string
): BaseResponseType<any> {
  console.log('调用更新实例接口，实例ID:', instanceId, '参数:', data, '地域:', region);
  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}`,
    method: 'PUT',
    data,
    headers: {
      'X-Region': region || '',
      'Content-Type': 'application/json'
    }
  });
}
