import { request } from '@baidu/bce-react-toolkit';

// 插件管理相关接口

/**
 * 查询插件市场插件列表
 * @param instanceId 实例ID
 * @param params 查询参数
 */
export const getPluginMarketList = (instanceId: string, params?: {
  pageNo?: number;
  pageSize?: number;
  orderBy?: string;
  order?: string;
}) => {
  console.log('调用查询插件市场插件接口，instanceId:', instanceId, 'params:', params);
  
  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/pluginMarket`,
    method: 'GET',
    params: {
      pageNo: params?.pageNo || 1,
      pageSize: params?.pageSize || 10,
      orderBy: params?.orderBy || 'createTime',
      order: params?.order || 'desc'
    }
  });
};

/**
 * 卸载插件
 * @param instanceId 实例ID
 * @param pluginName 插件名称
 */
export const uninstallPlugin = (instanceId: string, pluginName: string) => {
  console.log('调用卸载插件接口，instanceId:', instanceId, 'pluginName:', pluginName);
  
  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/pluginMarket/${pluginName}`,
    method: 'DELETE'
  });
};

/**
 * 创建外部认证插件
 * @param instanceId 实例ID
 * @param data 插件配置数据
 */
export const createExternalAuthPlugin = (instanceId: string, data: {
  enabled: boolean;
  name: string;
  description?: string;
  scope?: string;
  matchType?: string;
  routeList?: string[];
  httpService: string;
  statusOnError?: number;
}) => {
  console.log('调用创建外部认证插件接口，instanceId:', instanceId, 'data:', data);
  
  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/extAuth`,
    method: 'POST',
    data
  });
};

/**
 * 查询外部认证插件列表
 * @param instanceId 实例ID
 * @param params 查询参数
 */
export const getExternalAuthPluginList = (instanceId: string, params?: {
  name?: string;
  scope?: string;
  pageNo?: number;
  pageSize?: number;
  orderBy?: string;
  order?: string;
}) => {
  console.log('调用查询外部认证插件列表接口，instanceId:', instanceId, 'params:', params);

  // 过滤掉undefined的参数
  const filteredParams: any = {
    pageNo: params?.pageNo || 1,
    pageSize: params?.pageSize || 10,
    orderBy: params?.orderBy || 'createTime',
    order: params?.order || 'desc'
  };

  if (params?.name) {
    filteredParams.name = params.name;
  }

  if (params?.scope) {
    filteredParams.scope = params.scope;
  }

  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/extAuthList`,
    method: 'GET',
    params: filteredParams
  });
};

// 查询路由列表（含外部认证状态）
export const getRouteList = (instanceId: string) => {
  console.log('调用查询路由列表接口，instanceId:', instanceId);
  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/routes`,
    method: 'GET'
  });
};

// 查询外部认证插件详情
export const getExternalAuthPluginDetail = (instanceId: string, ruleName: string) => {
  console.log('调用查询外部认证插件详情接口，instanceId:', instanceId, 'ruleName:', ruleName);
  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/extAuth/${ruleName}`,
    method: 'GET'
  });
};



/**
 * 更新外部认证插件
 * @param instanceId 实例ID
 * @param pluginId 插件ID
 * @param data 更新数据
 */
export const updateExternalAuthPlugin = (instanceId: string, pluginId: string, data: {
  enabled?: boolean;
  name?: string;
  description?: string;
  scope?: string;
  matchType?: string;
  routeList?: string[];
  httpService?: string;
  statusOnError?: number;
}) => {
  console.log('调用更新外部认证插件接口，instanceId:', instanceId, 'pluginId:', pluginId, 'data:', data);
  
  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/extAuth/${pluginId}`,
    method: 'PUT',
    data
  });
};

/**
 * 删除外部认证插件
 * @param instanceId 实例ID
 * @param pluginId 插件ID
 */
export const deleteExternalAuthPlugin = (instanceId: string, pluginId: string) => {
  console.log('调用删除外部认证插件接口，instanceId:', instanceId, 'pluginId:', pluginId);
  
  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/extAuth/${pluginId}`,
    method: 'DELETE'
  });
};
