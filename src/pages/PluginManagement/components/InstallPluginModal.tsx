import React, { useState, useEffect } from 'react';
import { Modal, Button, toast } from 'acud';
import { createExternalAuthPlugin } from '@/apis/pluginManagement';
import styles from '../index.module.less';

interface InstallPluginModalProps {
  visible: boolean;
  plugin: any;
  instanceId: string;
  onCancel: () => void;
  onSuccess: () => void;
  onOpenDrawer: (pluginData: any) => void;
}

const InstallPluginModal: React.FC<InstallPluginModalProps> = ({
  visible,
  plugin,
  instanceId,
  onCancel,
  onSuccess,
  onOpenDrawer
}) => {
  const [loading, setLoading] = useState(false);

  // 获取插件显示名称
  const getPluginDisplayName = (pluginName: string) => {
    const nameMap: Record<string, string> = {
      'ext-auth': '外部认证'
    };
    return nameMap[pluginName] || pluginName;
  };

  // 获取插件描述
  const getPluginDescription = (pluginName: string) => {
    const descMap: Record<string, string> = {
      'ext-auth': '实现了向外部授权服务发送鉴权请求，以检查客户端请求是否得到授权。'
    };
    return descMap[pluginName] || '暂无描述';
  };

  // 处理安装插件 - 直接打开抽屉，不调用接口
  const handleInstall = () => {
    if (!plugin || !instanceId) return;

    console.log('点击安装并配置，直接打开抽屉:', plugin.pluginName);

    // 直接打开抽屉进行配置，传递模拟的插件数据
    onOpenDrawer({
      pluginName: plugin.pluginName,
      pluginId: `mock-${plugin.pluginName}-${Date.now()}`, // 模拟插件ID
      instanceId: instanceId,
      // 传递默认配置数据
      defaultConfig: {
        enabled: true,
        name: 'ext-auth-config', // 固定名称
        description: plugin.description || '外部认证插件',
        scope: 'gateway',
        httpService:
`endpoint_mode: envoy
endpoint:
  service_name: ext-auth.backend.svc.cluster.local
  service_port: 8090
  path_prefix: /auth
timeout: 1000`,
        statusOnError: 403
      }
    });

    // 关闭弹窗
    onCancel();
  };



  if (!plugin) return null;

  return (
    <Modal
      title="安装插件"
      visible={visible}
      onCancel={onCancel}
      width={520}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="install"
          type="primary"
          loading={loading}
          onClick={handleInstall}
        >
          安装并配置
        </Button>
      ]}
    >
      <div className={styles['install-modal']} style={{ marginTop: 16 }}>
        {/* 插件名称 */}
        <div className={styles['field-item']}>
          <div className={styles['field-label']}>
            插件名称
          </div>
          <div className={styles['field-value']}>
            {plugin ? getPluginDisplayName(plugin.pluginName) : ''}
          </div>
        </div>

        {/* 插件描述 */}
        <div className={styles['field-item']}>
          <div className={styles['field-label']}>
            插件描述
          </div>
          <div className={`${styles['field-value']} ${styles['multi-line']}`}>
            {plugin ? getPluginDescription(plugin.pluginName) : ''}
          </div>
        </div>

        {/* 网关实例ID */}
        <div className={styles['field-item']}>
          <div className={styles['field-label']}>
            网关实例ID
          </div>
          <div className={styles['field-value']}>
            {instanceId}
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default InstallPluginModal;
