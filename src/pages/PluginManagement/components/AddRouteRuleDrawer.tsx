import React, { useState, useEffect } from 'react';
import { Drawer, Button, Form, Input, Switch, Transfer, toast } from 'acud';
import { OutlinedClose } from 'acud-icon';
import { createExternalAuthPlugin, getRouteList } from '@/apis/pluginManagement';
import CodeEditor from './CodeEditor';

interface AddRouteRuleDrawerProps {
  visible: boolean;
  instanceId: string;
  onClose: () => void;
  onSuccess: () => void;
}

interface RouteItem {
  key: string;
  title: string;
  disabled?: boolean;
}

const AddRouteRuleDrawer: React.FC<AddRouteRuleDrawerProps> = ({
  visible,
  instanceId,
  onClose,
  onSuccess
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [routeList, setRouteList] = useState<RouteItem[]>([]);
  const [targetKeys, setTargetKeys] = useState<string[]>([]);
  // 默认YAML配置模板
  const defaultYamlConfig = 
`endpoint_mode: envoy
endpoint:
  service_name: ext-auth.backend.svc.cluster.local
  service_port: 8090
  path_prefix: /auth
timeout: 1000`;

  // 获取路由列表
  const fetchRouteList = async () => {
    if (!instanceId) return;

    try {
      console.log('获取路由列表，instanceId:', instanceId);
      
      const response = await getRouteList(instanceId);
      console.log('路由列表响应:', response);

      // 处理响应数据
      const responseData = (response as any)?.data || response;

      if (responseData?.success && responseData?.result) {
        const routes = responseData.result.map((route: any) => ({
          key: route.routeName,
          title: route.routeName,
          disabled: route.hasExtAuth // 已关联外部认证的路由不可选
        }));
        setRouteList(routes);
      } else {
        setRouteList([]);
      }
    } catch (error) {
      console.error('获取路由列表失败:', error);
      setRouteList([]);
    }
  };

  // 处理Transfer变化
  const handleTransferChange = (newTargetKeys: string[]) => {
    console.log('Transfer变化:', { newTargetKeys });
    setTargetKeys(newTargetKeys);
  };



  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      console.log('表单数据:', values);

      if (targetKeys.length === 0) {
        toast.error({
          message: '请选择至少一个目标路由',
          duration: 3
        });
        return;
      }

      setLoading(true);

      const requestData = {
        enabled: values.enabled,
        name: values.name,
        description: values.description || '路由级别外部认证插件',
        scope: 'route',
        matchType: 'blacklist',
        routeList: targetKeys,
        httpService: values.httpService,
        statusOnError: 403
      };

      console.log('创建路由级别插件请求数据:', requestData);

      const response = await createExternalAuthPlugin(instanceId, requestData);
      console.log('创建路由级别插件响应:', response);

      // 处理响应数据
      const responseData = (response as any)?.data || response;

      if (responseData?.success) {
        toast.success({
          message: '规则创建成功',
          duration: 3
        });
        onSuccess();
        handleClose();
      } else {
        toast.error({
          message: responseData?.message?.global || '创建失败',
          duration: 3
        });
      }
    } catch (error: any) {
      console.error('创建路由级别插件失败:', error);
      toast.error({
        message: error?.message?.global || '创建失败',
        duration: 3
      });
    } finally {
      setLoading(false);
    }
  };

  // 处理关闭
  const handleClose = () => {
    form.resetFields();
    setTargetKeys([]);
    onClose();
  };

  // 初始化表单数据
  useEffect(() => {
    if (visible) {
      fetchRouteList();
      form.setFieldsValue({
        enabled: true,
        httpService: defaultYamlConfig
      });
    }
  }, [visible, instanceId]);

  return (
    <Drawer
      title="添加规则（外部认证插件）"
      width={746}
      visible={visible}
      onClose={handleClose}
      closable={true}
      closeIcon={<OutlinedClose />}
      footer={
        <div style={{ textAlign: 'right' }}>
          <Button onClick={handleClose} style={{ marginRight: 8 }}>
            取消
          </Button>
          <Button type="primary" onClick={handleSubmit} loading={loading}>
            确定
          </Button>
        </div>
      }
    >
      <Form
        form={form}
        layout="horizontal"
        initialValues={{
          enabled: true,
          httpService: defaultYamlConfig
        }}
      >
        {/* 启用状态 */}
        <Form.Item
          label="启用状态"
          name="enabled"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>

        {/* 生效范围 */}
        <Form.Item label="生效范围">
          <span>路由级别</span>
        </Form.Item>

        {/* 规则名称 */}
        <Form.Item
          label="规则名称"
          name="name"
          rules={[
            { required: true, message: '请输入规则名称' },
            { min: 2, max: 64, message: '规则名称长度为2-64个字符' }
          ]}
        >
          <Input placeholder="请输入规则名称" />
        </Form.Item>

        {/* 目标路由 */}
        <Form.Item label="目标路由">
          <Transfer
            dataSource={routeList}
            targetKeys={targetKeys}
            onChange={handleTransferChange}
            showSearch
            render={item => item.title}
          />
        </Form.Item>

        {/* 插件规则 */}
        <Form.Item
          label="插件规则"
          name="httpService"
          rules={[
            { required: true, message: '请输入插件规则配置' }
          ]}
        >
          <CodeEditor
            value={form.getFieldValue('httpService') || defaultYamlConfig}
            onChange={(value) => form.setFieldsValue({ httpService: value })}
            language="yaml"
            height="200px"
          />
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default AddRouteRuleDrawer;
