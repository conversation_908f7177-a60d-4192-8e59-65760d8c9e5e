import React, { useRef, useEffect } from 'react';

interface CodeEditorProps {
  value: string;
  onChange: (value: string) => void;
  language?: string;
  height?: string;
  readOnly?: boolean;
}

const CodeEditor: React.FC<CodeEditorProps> = ({
  value,
  onChange,
  language = 'yaml',
  height = '200px',
  readOnly = false
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // 处理内容变化
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
  };

  // 处理Tab键缩进
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Tab') {
      e.preventDefault();
      const textarea = e.currentTarget;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      
      // 插入两个空格作为缩进
      const newValue = value.substring(0, start) + '  ' + value.substring(end);
      onChange(newValue);
      
      // 设置光标位置
      setTimeout(() => {
        textarea.selectionStart = textarea.selectionEnd = start + 2;
      }, 0);
    }
  };

  return (
    <div style={{ 
      position: 'relative',
      border: '1px solid #D4D6D9',
      borderRadius: '6px',
      backgroundColor: '#F7F7F9'
    }}>
      {/* 行号 */}
      <div style={{
        position: 'absolute',
        left: 0,
        top: 0,
        bottom: 0,
        width: '40px',
        backgroundColor: '#F7F7F9',
        borderRight: '1px solid #E8E9EB',
        padding: '8px 4px',
        fontSize: '12px',
        lineHeight: '20px',
        color: '#B8BABF',
        textAlign: 'right',
        fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
        userSelect: 'none'
      }}>
        {(value || '').split('\n').map((_, index) => (
          <div key={index + 1}>{index + 1}</div>
        ))}
      </div>

      {/* 代码编辑区域 */}
      <textarea
        ref={textareaRef}
        value={value}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        readOnly={readOnly}
        style={{
          width: '100%',
          height: height,
          border: 'none',
          outline: 'none',
          resize: 'none',
          padding: '8px 12px 8px 52px',
          fontSize: '14px',
          lineHeight: '20px',
          fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
          backgroundColor: 'transparent',
          color: '#2468F2',
          whiteSpace: 'pre',
          overflowWrap: 'normal',
          overflowX: 'auto'
        }}
        placeholder={readOnly ? '' : '请输入配置内容...'}
      />

      {/* 滚动条样式 */}
      <style jsx>{`
        textarea::-webkit-scrollbar {
          width: 5px;
          height: 5px;
        }
        textarea::-webkit-scrollbar-track {
          background: transparent;
        }
        textarea::-webkit-scrollbar-thumb {
          background: #E8E9EB;
          border-radius: 4px;
        }
        textarea::-webkit-scrollbar-thumb:hover {
          background: #D4D6D9;
        }
      `}</style>
    </div>
  );
};

export default CodeEditor;
