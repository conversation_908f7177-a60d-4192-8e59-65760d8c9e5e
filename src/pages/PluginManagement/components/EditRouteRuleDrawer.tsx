import React, { useState, useEffect } from 'react';
import { Drawer, Button, Form, Input, Switch, Transfer, toast } from 'acud';
import { OutlinedClose } from 'acud-icon';
import { 
  getExternalAuthPluginDetail, 
  updateExternalAuthPlugin, 
  getRouteList 
} from '@/apis/pluginManagement';
import CodeEditor from './CodeEditor';

interface EditRouteRuleDrawerProps {
  visible: boolean;
  instanceId: string;
  ruleName: string;
  onClose: () => void;
  onSuccess: () => void;
}

interface RouteItem {
  key: string;
  title: string;
  disabled?: boolean;
}

interface RuleDetail {
  enabled: boolean;
  name: string;
  description?: string;
  scope: string;
  matchType?: string;
  routeList?: string[];
  httpService: string;
  statusOnError: number;
  createTime: string;
  updateTime: string;
}

const EditRouteRuleDrawer: React.FC<EditRouteRuleDrawerProps> = ({
  visible,
  instanceId,
  ruleName,
  onClose,
  onSuccess
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [detailLoading, setDetailLoading] = useState(false);
  const [routeList, setRouteList] = useState<RouteItem[]>([]);
  const [targetKeys, setTargetKeys] = useState<string[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [ruleDetail, setRuleDetail] = useState<RuleDetail | null>(null);

  // 获取规则详情
  const fetchRuleDetail = async () => {
    if (!instanceId || !ruleName) return;

    try {
      setDetailLoading(true);
      console.log('获取规则详情，instanceId:', instanceId, 'ruleName:', ruleName);
      
      const response = await getExternalAuthPluginDetail(instanceId, ruleName);
      console.log('规则详情响应:', response);

      // 处理响应数据
      const responseData = (response as any)?.data || response;

      if (responseData?.success && responseData?.result) {
        const detail = responseData.result;
        setRuleDetail(detail);

        // 预填充表单数据
        form.setFieldsValue({
          enabled: detail.enabled || false,
          name: detail.name || '',
          description: detail.description || '',
          httpService: detail.httpService || ''
        });

        // 预填充目标路由
        if (detail.routeList && detail.routeList.length > 0) {
          const validRoutes = detail.routeList.filter(route => route && typeof route === 'string');
          setTargetKeys(validRoutes);
        }
      } else {
        toast.error({
          message: responseData?.message?.global || '获取规则详情失败',
          duration: 3
        });
      }
    } catch (error: any) {
      console.error('获取规则详情失败:', error);
      toast.error({
        message: error?.message?.global || '获取规则详情失败',
        duration: 3
      });
    } finally {
      setDetailLoading(false);
    }
  };

  // 获取路由列表
  const fetchRouteList = async () => {
    if (!instanceId) return;

    try {
      console.log('获取路由列表，instanceId:', instanceId);
      
      const response = await getRouteList(instanceId);
      console.log('路由列表响应:', response);

      // 处理响应数据
      const responseData = (response as any)?.data || response;

      if (responseData?.success && responseData?.result) {
        const routes = responseData.result.map((route: any) => ({
          key: route.routeName || '',
          title: route.routeName || '',
          disabled: route.hasExtAuth && !targetKeys.includes(route.routeName || '') // 已关联但不在当前规则中的路由不可选
        }));
        setRouteList(routes);
      } else {
        setRouteList([]);
      }
    } catch (error) {
      console.error('获取路由列表失败:', error);
      setRouteList([]);
    }
  };

  // 处理Transfer变化
  const handleTransferChange = (newTargetKeys: string[], direction: string, moveKeys: string[]) => {
    console.log('Transfer变化:', { newTargetKeys, direction, moveKeys });
    setTargetKeys(newTargetKeys);
  };

  // 处理Transfer选择变化
  const handleTransferSelectChange = (sourceSelectedKeys: string[], targetSelectedKeys: string[]) => {
    setSelectedKeys([...sourceSelectedKeys, ...targetSelectedKeys]);
  };

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      console.log('表单数据:', values);

      if (targetKeys.length === 0) {
        toast.error({
          message: '请选择至少一个目标路由',
          duration: 3
        });
        return;
      }

      setLoading(true);

      const requestData = {
        enabled: values.enabled || false,
        description: values.description || ruleDetail?.description || '路由级别外部认证插件',
        scope: 'route',
        matchType: ruleDetail?.matchType || 'blacklist',
        routeList: targetKeys.filter(key => key && typeof key === 'string'),
        httpService: values.httpService || '',
        statusOnError: ruleDetail?.statusOnError || 403
      };

      console.log('更新路由级别插件请求数据:', requestData);

      const response = await updateExternalAuthPlugin(instanceId, ruleName, requestData);
      console.log('更新路由级别插件响应:', response);

      // 处理响应数据
      const responseData = (response as any)?.data || response;

      if (responseData?.success) {
        toast.success({
          message: '规则更新成功',
          duration: 3
        });
        onSuccess();
        handleClose();
      } else {
        toast.error({
          message: responseData?.message?.global || '更新失败',
          duration: 3
        });
      }
    } catch (error: any) {
      console.error('更新路由级别插件失败:', error);
      toast.error({
        message: error?.message?.global || '更新失败',
        duration: 3
      });
    } finally {
      setLoading(false);
    }
  };

  // 处理关闭
  const handleClose = () => {
    form.resetFields();
    setTargetKeys([]);
    setSelectedKeys([]);
    setRuleDetail(null);
    onClose();
  };

  // 初始化数据
  useEffect(() => {
    if (visible && instanceId && ruleName) {
      fetchRuleDetail();
      fetchRouteList();
    }
  }, [visible, instanceId, ruleName]);

  // 当规则详情加载完成后，更新路由列表的disabled状态
  useEffect(() => {
    if (ruleDetail && routeList.length > 0) {
      const updatedRouteList = routeList.map(route => ({
        ...route,
        disabled: route.disabled && !targetKeys.includes(route.key)
      }));
      setRouteList(updatedRouteList);
    }
  }, [ruleDetail, targetKeys]);

  return (
    <Drawer
      title="编辑规则（外部认证插件）"
      width={746}
      visible={visible}
      onClose={handleClose}
      closable={true}
      closeIcon={<OutlinedClose />}
      footer={
        <div style={{ textAlign: 'right' }}>
          <Button onClick={handleClose} style={{ marginRight: 8 }}>
            取消
          </Button>
          <Button type="primary" onClick={handleSubmit} loading={loading}>
            更新
          </Button>
        </div>
      }
    >
      {detailLoading ? (
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          加载规则详情中...
        </div>
      ) : (
        <Form
          form={form}
          layout="horizontal"
        >
          {/* 启用状态 */}
          <Form.Item
            label="启用状态"
            name="enabled"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          {/* 生效范围 */}
          <Form.Item label="生效范围">
            <span>路由级别</span>
          </Form.Item>

          {/* 规则名称 */}
          <Form.Item label="规则名称">
            <span>{ruleDetail?.name || ruleName}</span>
          </Form.Item>

          {/* 目标路由 */}
          <Form.Item label="目标路由">
            <Transfer
              dataSource={routeList}
              targetKeys={targetKeys}
              selectedKeys={selectedKeys}
              onChange={handleTransferChange}
              onSelectChange={handleTransferSelectChange}
              titles={['可选项', '已选项']}
              showSearch
              searchPlaceholder="搜索路由"
              render={item => item.title}
              style={{ marginBottom: 16 }}
            />
          </Form.Item>

          {/* 插件规则 */}
          <Form.Item
            label="插件规则"
            name="httpService"
            rules={[
              { required: true, message: '请输入插件规则配置' }
            ]}
          >
            <CodeEditor
              language="yaml"
              height="200px"
              placeholder="请输入YAML格式的插件配置"
            />
          </Form.Item>
        </Form>
      )}
    </Drawer>
  );
};

export default EditRouteRuleDrawer;
