import React, { useState, useEffect } from 'react';
import { Table, Pagination, Popconfirm, toast, Empty, Tooltip, Button } from 'acud';
import { OutlinedPlus, OutlinedRefresh, OutlinedPlusNew } from 'acud-icon';
import { getPluginMarketList, getExternalAuthPluginList, uninstallPlugin } from '@/apis/pluginManagement';
import styles from '../index.module.less';

interface InstalledPluginsProps {
  instanceId: string;
  refreshKey: number;
  onConfigure: (plugin: any) => void;
  onInstallPlugin: () => void;
}

interface InstalledPlugin {
  pluginName: string;
  pluginType: string;
  installStatus: string;
  routeEnabledCount: number;
  gatewayEnabledCount: number;
  ruleCount: {
    total: number;
    routeRuleCount: number;
    gatewayRuleCount: number;
  };
  createTime?: string;
  displayName?: string;
  description?: string;
}

const InstalledPlugins: React.FC<InstalledPluginsProps> = ({
  instanceId,
  refreshKey,
  onConfigure,
  onInstallPlugin
}) => {
  const [loading, setLoading] = useState(false);
  const [pluginList, setPluginList] = useState<InstalledPlugin[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });

  // 获取已安装插件列表
  const fetchInstalledPlugins = async (pageNo = 1, pageSize = 10) => {
    if (!instanceId) return;

    try {
      setLoading(true);
      console.log('获取已安装插件列表，instanceId:', instanceId);

      // 首先获取插件市场数据，确定哪些插件已安装
      const marketResponse = await getPluginMarketList(instanceId, {
        pageNo: 1,
        pageSize: 100, // 获取所有插件
        orderBy: 'createTime',
        order: 'desc'
      });

      console.log('插件市场响应:', marketResponse);

      // 处理插件市场响应数据
      const marketData = (marketResponse as any)?.data || marketResponse;
      if (marketData?.success && marketData?.page?.result) {
        // 过滤出已安装的插件
        const installedPlugins = marketData.page.result.filter(
          (plugin: InstalledPlugin) => plugin.installStatus === 'installed'
        );

        console.log('已安装的插件:', installedPlugins);

        if (installedPlugins.length === 0) {
          setPluginList([]);
          setPagination(prev => ({ ...prev, total: 0 }));
          setLoading(false);
          return;
        }

        // 获取外部认证插件的详细规则列表来计算准确的规则数量
        const extAuthResponse = await getExternalAuthPluginList(instanceId, {
          pageNo: 1,
          pageSize: 100, // 获取所有规则
          orderBy: 'createTime',
          order: 'desc'
        });

        console.log('外部认证插件规则列表响应:', extAuthResponse);

        // 处理外部认证响应数据
        const extAuthData = (extAuthResponse as any)?.data || extAuthResponse;

        // 计算准确的规则数量统计
        const enrichedPlugins = installedPlugins.map((plugin: InstalledPlugin) => {
          let actualRuleCount = { total: 0, routeRuleCount: 0, gatewayRuleCount: 0 };
          let routeEnabledCount = 0;
          let gatewayEnabledCount = 0;

          if (extAuthData?.success && extAuthData?.page?.result) {
            const rules = extAuthData.page.result;

            // 统计网关级别规则
            const gatewayRules = rules.filter((rule: any) => rule.scope === 'gateway');
            actualRuleCount.gatewayRuleCount = gatewayRules.length;
            gatewayEnabledCount = gatewayRules.filter((rule: any) => rule.enabled).length;

            // 统计路由级别规则
            const routeRules = rules.filter((rule: any) => rule.scope === 'route');
            actualRuleCount.routeRuleCount = routeRules.length;
            routeEnabledCount = routeRules.filter((rule: any) => rule.enabled).length;

            actualRuleCount.total = actualRuleCount.gatewayRuleCount + actualRuleCount.routeRuleCount;
          }

          return {
            ...plugin,
            displayName: getPluginDisplayName(plugin.pluginName),
            description: getPluginDescription(plugin.pluginName),
            createTime: plugin.createTime || new Date().toISOString(),
            // 使用实际计算的规则数量
            ruleCount: actualRuleCount,
            routeEnabledCount,
            gatewayEnabledCount
          };
        });

        console.log('增强后的插件列表:', enrichedPlugins);

        setPluginList(enrichedPlugins);
        setPagination({
          current: pageNo,
          pageSize: pageSize,
          total: enrichedPlugins.length
        });
      } else {
        setPluginList([]);
        setPagination(prev => ({ ...prev, total: 0 }));
      }
    } catch (error) {
      console.error('获取已安装插件列表失败:', error);
      setPluginList([]);
    } finally {
      setLoading(false);
    }
  };

  // 获取插件显示名称
  const getPluginDisplayName = (pluginName: string) => {
    const nameMap: Record<string, string> = {
      'ext-auth': '外部认证'
    };
    return nameMap[pluginName] || pluginName;
  };

  // 获取插件描述
  const getPluginDescription = (pluginName: string) => {
    const descMap: Record<string, string> = {
      'ext-auth': '实现了向外部授权服务发送鉴权请求，以检查客户端请求是否得到授权。'
    };
    return descMap[pluginName] || '暂无描述';
  };

  // 获取插件类型显示名称
  const getPluginTypeDisplayName = (pluginType: string) => {
    const typeMap: Record<string, string> = {
      'auth': '认证鉴权'
    };
    return typeMap[pluginType] || pluginType;
  };



  // 处理规则配置
  const handleConfigure = (plugin: InstalledPlugin) => {
    console.log('点击规则配置:', plugin);
    onConfigure({
      pluginName: plugin.pluginName,
      pluginId: `installed-${plugin.pluginName}`,
      instanceId: instanceId,
      // 使用默认配置数据
      defaultConfig: {
        enabled: true,
        name: 'ext-auth-config', // 固定名称
        description: plugin.description || '外部认证插件',
        scope: 'gateway',
        httpService:
`endpoint_mode: envoy
endpoint:
  service_name: ext-auth.backend.svc.cluster.local
  service_port: 8090
  path_prefix: /auth
timeout: 1000`,
        statusOnError: 403
      }
    });
  };

  // 卸载插件
  const handleUninstall = async (plugin: InstalledPlugin) => {
    try {
      console.log('卸载插件:', plugin.pluginName);

      const response = await uninstallPlugin(instanceId, plugin.pluginName);

      // 处理响应数据
      const responseData = (response as any)?.data || response;

      if (responseData?.success) {
        toast.success({
          message: `插件 ${plugin.displayName} 卸载成功`,
          duration: 3
        });
        fetchInstalledPlugins(pagination.current, pagination.pageSize);
      } else {
        toast.error({
          message: responseData?.message?.global || '卸载失败',
          duration: 3
        });
      }
    } catch (error: any) {
      console.error('卸载插件失败:', error);
      const errorData = error?.response?.data || error;
      toast.error({
        message: errorData?.message?.global || '卸载失败',
        duration: 3
      });
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '插件名称',
      dataIndex: 'displayName',
      key: 'displayName',
      width: 120
    },
    {
      title: '规则数量',
      key: 'ruleCount',
      width: 120,
      render: (_: any, record: InstalledPlugin) => {
        const { ruleCount } = record;
        const totalCount = ruleCount?.total || 0;

        // 构建气泡提示内容
        const tooltipContent = (
          <div>
            <div>路由级规则：{ruleCount?.routeRuleCount || 0}条（{record.routeEnabledCount || 0} 条已启用）</div>
            <div>网关级规则：{ruleCount?.gatewayRuleCount || 0}条（{record.gatewayEnabledCount || 0} 条已启用）</div>
          </div>
        );

        return (
          <Tooltip title={tooltipContent} placement="bottom">
            <div className={styles['rule-count-cell']}>
              <span className={styles['rule-count-text']}>{totalCount} 条</span>
              <div className={styles['ant-line']}></div>
            </div>
          </Tooltip>
        );
      }
    },
    {
      title: '插件类型',
      dataIndex: 'pluginType',
      key: 'pluginType',
      width: 100,
      render: (pluginType: string) => getPluginTypeDisplayName(pluginType)
    },

    {
      title: '操作',
      key: 'actions',
      width: 160,
      render: (_: any, record: InstalledPlugin) => (
        <div className={styles['action-buttons']}>
          <Button
            type="text"
            onClick={() => handleConfigure(record)}
          >
            规则配置
          </Button>
          <Popconfirm
            title={`确定要卸载插件 ${record.displayName} 吗？卸载后将删除该插件的所有配置规则`}
            onConfirm={() => handleUninstall(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
            >
              卸载
            </Button>
          </Popconfirm>
        </div>
      )
    }
  ];

  // 处理分页变化
  const handlePageChange = (page: number, pageSize?: number) => {
    console.log('分页变化:', page, pageSize);
    fetchInstalledPlugins(page, pageSize || pagination.pageSize);
  };

  // 处理刷新
  const handleRefresh = () => {
    console.log('刷新已安装插件列表');
    fetchInstalledPlugins(pagination.current, pagination.pageSize);
  };

  useEffect(() => {
    fetchInstalledPlugins();
  }, [instanceId, refreshKey]);

  return (
    <div>
      {/* 操作按钮区域 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16
      }}>
        <Button
          type="primary"
          icon={<OutlinedPlus />}
          onClick={onInstallPlugin}
        >
          安装插件
        </Button>
        <Button
          icon={<OutlinedRefresh />}
          onClick={handleRefresh}
        >
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={pluginList}
        loading={loading}
        pagination={false}
        rowKey="pluginName"
        locale={{
          emptyText: (
            <Empty
              description={
                <span>
                  暂无已安装插件。
                  <a onClick={onInstallPlugin} style={{ marginLeft: '4px', cursor: 'pointer' }}>
                    <OutlinedPlusNew style={{ marginRight: '4px' }} />
                    安装插件
                  </a>
                </span>
              }
            />
          )
        }}
      />

      {pluginList.length > 0 && (
        <div style={{ marginTop: 16, textAlign: 'right' }}>
          <Pagination
            current={pagination.current}
            pageSize={pagination.pageSize}
            total={pagination.total}
            onChange={handlePageChange}
            showSizeChanger
            showQuickJumper
            showTotal={(total, range) =>
              `共 ${total} 条记录，第 ${range[0]}-${range[1]} 条`
            }
          />
        </div>
      )}
    </div>
  );
};

export default InstalledPlugins;
