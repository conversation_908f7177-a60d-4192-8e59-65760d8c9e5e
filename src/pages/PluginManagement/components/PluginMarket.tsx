import React, { useState, useEffect } from 'react';
import { Tag, Empty } from 'acud';
import { getPluginMarketList } from '@/apis/pluginManagement';
import styles from '../index.module.less';

interface PluginMarketProps {
  instanceId: string;
  onInstall: (plugin: any) => void;
  onConfigure: (plugin: any) => void;
  refreshKey: number;
}

interface PluginInfo {
  pluginName: string;
  pluginType: string;
  installStatus: string;
  displayName?: string;
  description?: string;
  category?: string;
}

const PluginMarket: React.FC<PluginMarketProps> = ({
  instanceId,
  onInstall,
  onConfigure,
  refreshKey
}) => {
  const [loading, setLoading] = useState(false);
  const [pluginList, setPluginList] = useState<PluginInfo[]>([]);

  // 获取插件市场数据 - 显示所有插件（包括已安装的）
  const fetchPluginMarket = async () => {
    if (!instanceId) return;

    try {
      setLoading(true);
      console.log('获取插件市场数据，instanceId:', instanceId);

      // 所有可用插件数据，目前仅包含外部认证插件
      const allPluginData = [
        {
          pluginName: 'ext-auth',
          pluginType: 'auth',
          installStatus: 'uninstalled',
          displayName: '外部认证',
          description: '实现了向外部授权服务发送鉴权请求，以检查客户端请求是否得到授权。',
          category: '认证鉴权'
        }
      ];

      // 检查插件安装状态
      const response = await getPluginMarketList(instanceId, {
        pageNo: 1,
        pageSize: 100
      });

      console.log('插件市场数据响应:', response);

      let pluginsWithStatus = allPluginData;

      if (response?.success && response?.page?.result) {
        // 获取已安装的插件名称列表
        const installedPluginNames = response.page.result
          .filter((plugin: PluginInfo) => plugin.installStatus === 'installed')
          .map((plugin: PluginInfo) => plugin.pluginName);

        // 更新插件安装状态
        pluginsWithStatus = allPluginData.map(plugin => ({
          ...plugin,
          installStatus: installedPluginNames.includes(plugin.pluginName) ? 'installed' : 'uninstalled'
        }));
      }

      setPluginList(pluginsWithStatus);
    } catch (error) {
      console.error('获取插件市场数据失败:', error);
      // 即使接口失败，也显示所有可用插件（默认为未安装状态）
      setPluginList([
        {
          pluginName: 'ext-auth',
          pluginType: 'auth',
          installStatus: 'uninstalled',
          displayName: '外部认证',
          description: '实现了向外部授权服务发送鉴权请求，以检查客户端请求是否得到授权。',
          category: '认证鉴权'
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  // 获取标签颜色 - 根据设计稿使用橙色标签
  const getTagColor = (pluginType: string) => {
    // 所有认证类插件都使用橙色标签
    return 'warning';
  };

  // 处理安装插件
  const handleInstall = (plugin: PluginInfo) => {
    console.log('点击安装插件:', plugin);
    onInstall(plugin);
  };

  // 处理前往配置
  const handleConfigure = (plugin: PluginInfo) => {
    console.log('点击前往配置:', plugin);
    onConfigure(plugin);
  };

  useEffect(() => {
    fetchPluginMarket();
  }, [instanceId, refreshKey]);

  if (loading) {
    return <div className={styles['empty-state']}>加载中...</div>;
  }

  if (pluginList.length === 0) {
    return (
      <div className={styles['empty-state']}>
        <Empty description="暂无可用插件" />
      </div>
    );
  }

  return (
    <div className={styles['plugin-market']}>
      <div className={styles['plugin-cards']}>
        {pluginList.map((plugin) => (
          <div key={plugin.pluginName} className={styles['plugin-card']}>
            <div className={styles['card-header']}>
              <div className={styles['title-section']}>
                <h3 className={styles['plugin-title']}>
                  {plugin.displayName}
                </h3>
                {plugin.installStatus === 'installed' && (
                  <div className={styles['installed-status']}>
                    <span className={styles['status-dot']}></span>
                    <span className={styles['status-text']}>已安装</span>
                  </div>
                )}
              </div>
              <Tag
                color={getTagColor(plugin.pluginType)}
                className={styles['plugin-tag']}
              >
                {plugin.category}
              </Tag>
            </div>

            <div className={styles['plugin-description']}>
              {plugin.description}
            </div>

            <div className={styles['card-footer']}>
              <span className={styles['plugin-intro']}>
                插件介绍
              </span>
              <span
                className={styles['install-action']}
                onClick={() => plugin.installStatus === 'installed'
                  ? handleConfigure(plugin)
                  : handleInstall(plugin)
                }
              >
                {plugin.installStatus === 'installed' ? '前往配置' : '安装'}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PluginMarket;
