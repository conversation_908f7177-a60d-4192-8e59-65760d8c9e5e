import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Button, Switch, Table, Pagination, toast, Popconfirm, Tooltip } from 'acud';
import { OutlinedClose, OutlinedPlus, OutlinedCopy } from 'acud-icon';
import {
  getExternalAuthPluginDetail,
  updateExternalAuthPlugin,
  getExternalAuthPluginList,
  createExternalAuthPlugin,
  deleteExternalAuthPlugin
} from '@/apis/pluginManagement';
import CodeEditor from './CodeEditor';
import AddRouteRuleDrawer from './AddRouteRuleDrawer';
import EditRouteRuleDrawer from './EditRouteRuleDrawer';
import styles from '../index.module.less';

interface PluginConfigDrawerProps {
  visible: boolean;
  pluginData: any;
  onClose: () => void;
}

interface RouteRule {
  name: string;
  enabled: boolean;
  scope: string;
  matchType?: string;
  routeList?: string[];
  httpService: string;
  statusOnError: number;
  createTime: string;
  updateTime: string;
}

interface PluginConfig {
  id: string;
  enabled: boolean;
  name: string;
  description: string;
  scope: string;
  matchType?: string;
  routeList?: string[];
  httpService: string;
  statusOnError: number;
}

const PluginConfigDrawer: React.FC<PluginConfigDrawerProps> = ({
  visible,
  pluginData,
  onClose
}) => {
  const [activeTab, setActiveTab] = useState<'gateway' | 'route'>('gateway');
  const [loading, setLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [config, setConfig] = useState<PluginConfig | null>(null);
  const [routeRules, setRouteRules] = useState<any[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [addRuleDrawerVisible, setAddRuleDrawerVisible] = useState(false);
  const [editRuleDrawerVisible, setEditRuleDrawerVisible] = useState(false);
  const [editingRuleName, setEditingRuleName] = useState<string>('');

  // 获取插件配置详情
  const fetchPluginConfig = async () => {
    if (!pluginData?.pluginId || !pluginData?.instanceId) return;

    try {
      setLoading(true);
      console.log('获取插件配置详情:', pluginData);

      // 如果有默认配置，直接使用默认配置
      if (pluginData.defaultConfig) {
        console.log('使用默认配置:', pluginData.defaultConfig);
        setConfig({
          id: pluginData.pluginId,
          ...pluginData.defaultConfig
        });
        setLoading(false);
        return;
      }

      // 否则从接口获取配置
      const response = await getExternalAuthPluginDetail(
        pluginData.instanceId,
        pluginData.pluginId
      );

      // 处理响应数据
      const responseData = (response as any)?.data || response;

      if (responseData?.success && responseData?.result) {
        setConfig(responseData.result);
      }
    } catch (error) {
      console.error('获取插件配置失败:', error);
      toast.error({
        message: '获取插件配置失败',
        duration: 3
      });
    } finally {
      setLoading(false);
    }
  };

  // 获取路由级别规则列表
  const fetchRouteRules = async (pageNo = 1, pageSize = 10) => {
    if (!pluginData?.instanceId) {
      console.log('fetchRouteRules: 缺少instanceId');
      return;
    }

    try {
      setLoading(true);
      console.log('fetchRouteRules: 开始获取路由级别规则列表', {
        instanceId: pluginData.instanceId,
        pageNo,
        pageSize
      });

      const response = await getExternalAuthPluginList(pluginData.instanceId, {
        scope: 'route', // 只获取路由级别的规则
        pageNo,
        pageSize,
        orderBy: 'updateTime',
        order: 'desc'
      });

      console.log('fetchRouteRules: 路由级别规则列表响应:', response);

      // 处理响应数据
      const responseData = (response as any)?.data || response;

      if (responseData?.success && responseData?.page?.result) {
        setRouteRules(responseData.page.result);
        setPagination({
          current: responseData.page.pageNo,
          pageSize: responseData.page.pageSize,
          total: responseData.page.totalCount
        });
      } else {
        setRouteRules([]);
        setPagination(prev => ({ ...prev, total: 0 }));
      }
    } catch (error) {
      console.error('获取路由级别规则列表失败:', error);
      setRouteRules([]);
    } finally {
      setLoading(false);
    }
  };

  // 编辑路由规则
  const handleEditRule = (rule: RouteRule) => {
    console.log('编辑路由规则:', rule);
    setEditingRuleName(rule.name);
    setEditRuleDrawerVisible(true);
  };

  // 启用/停用路由规则
  const handleToggleRule = async (rule: RouteRule) => {
    try {
      console.log('切换路由规则状态:', rule);

      const response = await updateExternalAuthPlugin(
        pluginData.instanceId,
        rule.name, // 使用name作为pluginId
        {
          enabled: !rule.enabled
        }
      );

      // 处理响应数据
      const responseData = (response as any)?.data || response;

      if (responseData?.success) {
        toast.success({
          message: `规则已${!rule.enabled ? '启用' : '停用'}`,
          duration: 2
        });
        fetchRouteRules(pagination.current, pagination.pageSize);
      } else {
        toast.error({
          message: responseData?.message?.global || '操作失败',
          duration: 3
        });
      }
    } catch (error: any) {
      console.error('切换规则状态失败:', error);
      const errorData = error?.response?.data || error;
      toast.error({
        message: errorData?.message?.global || '操作失败',
        duration: 3
      });
    }
  };

  // 删除路由规则
  const handleDeleteRule = async (rule: RouteRule) => {
    try {
      console.log('删除路由规则:', rule);

      const response = await deleteExternalAuthPlugin(
        pluginData.instanceId,
        rule.name // 使用name作为ruleName
      );

      console.log('删除路由规则响应:', response);

      // 处理响应数据
      const responseData = (response as any)?.data || response;

      if (responseData?.success) {
        toast.success({
          message: '规则删除成功',
          duration: 3
        });
        // 刷新列表
        fetchRouteRules(pagination.current, pagination.pageSize);
      } else {
        toast.error({
          message: responseData?.message?.global || '删除失败',
          duration: 3
        });
      }
    } catch (error: any) {
      console.error('删除规则失败:', error);
      const errorData = error?.response?.data || error;
      toast.error({
        message: errorData?.message?.global || '删除失败',
        duration: 3
      });
    }
  };

  // 保存网关级别配置
  const handleSaveGatewayConfig = async () => {
    if (!config || !pluginData?.pluginId || !pluginData?.instanceId) return;

    try {
      setSaveLoading(true);
      console.log('保存网关级别配置:', config);

      // 模拟保存延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 模拟保存成功
      console.log('配置已保存（模拟）:', {
        enabled: config.enabled,
        name: config.name,
        description: config.description,
        scope: config.scope,
        httpService: config.httpService,
        statusOnError: config.statusOnError
      });

      toast.success({
        message: '配置保存成功',
        duration: 3
      });
    } catch (error: any) {
      console.error('保存配置失败:', error);
      toast.error({
        message: '保存失败',
        duration: 3
      });
    } finally {
      setSaveLoading(false);
    }
  };

  // 处理启用状态变化
  const handleEnabledChange = (enabled: boolean) => {
    if (config) {
      setConfig({ ...config, enabled });
    }
  };

  // 处理插件规则变化
  const handleHttpServiceChange = (value: string) => {
    if (config) {
      setConfig({ ...config, httpService: value });
    }
  };

  // 处理标签页切换
  const handleTabChange = (tab: 'gateway' | 'route') => {
    console.log('handleTabChange: 切换到标签页:', tab);
    setActiveTab(tab);
    if (tab === 'route') {
      console.log('handleTabChange: 切换到路由级别，开始获取路由规则');
      fetchRouteRules();
    }
  };

  // 处理添加规则
  const handleAddRule = () => {
    console.log('打开添加规则抽屉');
    setAddRuleDrawerVisible(true);
  };

  // 处理添加规则成功
  const handleAddRuleSuccess = () => {
    console.log('添加规则成功，刷新路由规则列表');
    fetchRouteRules(pagination.current, pagination.pageSize);
  };

  // 处理确定按钮 - 仅创建网关级别的外部认证插件
  const handleConfirm = async () => {
    console.log('handleConfirm被调用');
    console.log('config:', config);
    console.log('pluginData:', pluginData);
    console.log('activeTab:', activeTab);

    if (!config || !pluginData?.instanceId) {
      console.warn('缺少必要数据，config:', !!config, 'instanceId:', pluginData?.instanceId);
      return;
    }

    // 确保只在网关级别标签页时执行
    if (activeTab !== 'gateway') {
      console.warn('确定按钮仅在网关级别标签页时可用，当前标签页:', activeTab);
      return;
    }

    try {
      setConfirmLoading(true);
      console.log('创建网关级别外部认证插件，配置数据:', config);

      // 严格按照接口文档规范构建请求参数
      const createData = {
        enabled: config.enabled,                    // boolean, 必填
        name: config.name,                          // string, 必填, 2-64个字符
        description: config.description,            // string, 可选, 最大200个字符
        scope: 'gateway',                          // string, 固定为gateway（网关维度）
        httpService: config.httpService,           // string, 必填, YAML格式字符串
        statusOnError: config.statusOnError || 403 // integer, 可选, 默认403, 范围400-599
      };

      console.log('请求参数:', createData);

      const response = await createExternalAuthPlugin(pluginData.instanceId, createData);

      // 处理响应数据
      const responseData = (response as any)?.data || response;

      if (responseData?.success) {
        console.log('网关级别外部认证插件创建成功:', responseData);
        toast.success({
          message: '网关级别插件创建成功',
          duration: 3
        });
        onClose(); // 关闭抽屉
      } else {
        throw new Error(responseData?.message?.global || '创建失败');
      }
    } catch (error: any) {
      console.error('创建网关级别插件失败:', error);
      toast.error({
        message: error?.message?.global || '创建失败',
        duration: 3
      });
    } finally {
      setConfirmLoading(false);
    }
  };

  // 直接返回接口时间格式，不做转换
  const formatTime = (timeStr: string) => {
    return timeStr || '-';
  };

  // 获取规则内容概要
  const getRuleContentSummary = (httpService: string) => {
    if (!httpService) return '-';
    try {
      // 提取服务名称和端口
      const serviceMatch = httpService.match(/service_name:\s*([^\n]+)/);
      const portMatch = httpService.match(/service_port:\s*(\d+)/);

      if (serviceMatch && portMatch) {
        const summary = `${serviceMatch[1]}:${portMatch[1]}`;
        // 限制显示长度，超过30个字符截断
        return summary.length > 30 ? `${summary.substring(0, 30)}...` : summary;
      }
      return '查看详情';
    } catch {
      return '查看详情';
    }
  };

  // 复制规则内容
  const handleCopyRuleContent = async (httpService: string) => {
    try {
      await navigator.clipboard.writeText(httpService);
      toast.success({
        message: '规则内容已复制到剪贴板',
        duration: 2
      });
    } catch (error) {
      console.error('复制失败:', error);
      toast.error({
        message: '复制失败，请手动复制',
        duration: 2
      });
    }
  };

  // 路由级别表格列定义
  const routeColumns = [
    {
      title: '规则名称',
      dataIndex: 'name',
      key: 'name',
      width: 140
    },
    {
      title: '目标路由',
      key: 'targetRoute',
      width: 160,
      render: (_: any, record: RouteRule) => {
        // 根据scope显示不同的路由信息
        if (record.scope === 'gateway') {
          return '全局路由';
        }
        // 路由级别显示routeList数组
        if (record.routeList && record.routeList.length > 0) {
          return record.routeList.join('、');
        }
        return '-';
      }
    },
    {
      title: '生效状态',
      dataIndex: 'enabled',
      key: 'enabled',
      width: 110,
      render: (enabled: boolean) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <div
            style={{
              width: '8px',
              height: '8px',
              borderRadius: '50%',
              backgroundColor: enabled ? '#30BF13' : '#D8D8D8'
            }}
          />
          <span>{enabled ? '已启用' : '已停用'}</span>
        </div>
      )
    },
    {
      title: '规则内容',
      dataIndex: 'httpService',
      key: 'httpService',
      width: 200,
      render: (httpService: string) => {
        const summary = getRuleContentSummary(httpService);

        return (
          <Tooltip
            title={
              <div>
                <pre style={{
                  whiteSpace: 'pre-wrap',
                  margin: 0,
                  fontSize: '12px',
                  maxWidth: '400px'
                }}>
                  {httpService}
                </pre>
                <div style={{
                  textAlign: 'right',
                  marginTop: '8px',
                  borderTop: '1px solid #d9d9d9',
                  paddingTop: '8px'
                }}>
                  <span
                    style={{
                      cursor: 'pointer',
                      color: '#1890ff',
                      fontSize: '12px'
                    }}
                    onClick={() => handleCopyRuleContent(httpService)}
                  >
                    <OutlinedCopy style={{ marginRight: '4px' }} />
                    复制
                  </span>
                </div>
              </div>
            }
            placement="bottom"
          >
            <span style={{ cursor: 'pointer' }}>{summary}</span>
          </Tooltip>
        );
      }
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: 150,
      render: (time: string) => formatTime(time)
    },
    {
      title: '操作',
      key: 'actions',
      width: 180,
      render: (_: any, record: RouteRule) => (
        <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
          <a
            style={{
              color: '#2468F2',
              cursor: 'pointer',
              fontSize: '12px'
            }}
            onClick={() => handleEditRule(record)}
          >
            编辑
          </a>
          {/* <a
            style={{
              color: '#2468F2',
              cursor: 'pointer',
              fontSize: '12px'
            }}
            onClick={() => handleToggleRule(record)}
          >
            {record.enabled ? '停用' : '启用'}
          </a> */}
          <Popconfirm
            title={`确定要删除规则 ${record.name} 吗？删除后将无法恢复`}
            onConfirm={() => handleDeleteRule(record)}
            okText="确定"
            cancelText="取消"
          >
            <a
              style={{
                color: '#2468F2',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              删除
            </a>
          </Popconfirm>
        </div>
      )
    }
  ];

  useEffect(() => {
    if (visible && pluginData) {
      fetchPluginConfig();
      // 如果当前是路由级别标签页，也要获取路由规则
      if (activeTab === 'route') {
        fetchRouteRules();
      }
    }
  }, [visible, pluginData, activeTab]);

  return (
    <Drawer
      title="规则配置（外部认证插件）"
      visible={visible}
      onClose={onClose}
      width={746}
      className={styles['plugin-config-drawer']}
      closeIcon={<OutlinedClose />}
      footer={
        // 仅在网关级别标签页时显示底部按钮
        activeTab === 'gateway' ? (
          <div className={styles['drawer-footer']}>
            <Button onClick={onClose}>
              取消
            </Button>
            <Button
              type="primary"
              loading={confirmLoading}
              onClick={handleConfirm}
            >
              确定
            </Button>
          </div>
        ) : null
      }
    >
      <div className={styles['drawer-content']}>
        {/* 标签页切换按钮 */}
        <div className={styles['tab-buttons']}>
          <Button
            type={activeTab === 'gateway' ? 'enhance' : 'default'}
            size="small"
            onClick={() => handleTabChange('gateway')}
          >
            网关级别
          </Button>
          <Button
            type={activeTab === 'route' ? 'enhance' : 'default'}
            size="small"
            onClick={() => handleTabChange('route')}
          >
            路由级别
          </Button>
        </div>

        {/* 网关级别配置 */}
        {activeTab === 'gateway' && config && (
          <div className={styles['gateway-config']}>
            {/* 启用状态 */}
            <div className={styles['config-item']}>
              <span className={styles['config-label']}>启用状态：</span>
              <Switch
                checked={config.enabled}
                onChange={handleEnabledChange}
                checkedChildren="开"
                unCheckedChildren="关"
              />
            </div>

            {/* 生效范围 */}
            <div className={styles['config-item']}>
              <span className={styles['config-label']}>生效范围：</span>
              <span className={styles['config-value']}>网关级别</span>
            </div>

            {/* 插件规则 */}
            <div className={styles['config-item']}>
              <span className={styles['config-label']}>插件规则：</span>
              <div className={styles['code-editor-container']}>
                <CodeEditor
                  value={config.httpService}
                  onChange={handleHttpServiceChange}
                  language="yaml"
                  height="300px"
                />
                <div className={styles['editor-actions']}>
                  <Button
                    type="primary"
                    loading={saveLoading}
                    onClick={handleSaveGatewayConfig}
                  >
                    保存
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 路由级别配置 */}
        {activeTab === 'route' && (
          <div>
            {/* 添加规则按钮 */}
            <div style={{ marginBottom: 16 }}>
              <Button
                type="primary"
                icon={<OutlinedPlus />}
                onClick={handleAddRule}
              >
                添加规则
              </Button>
            </div>

            {/* 规则列表 */}
            <Table
              columns={routeColumns}
              dataSource={routeRules}
              loading={loading}
              pagination={false}
              rowKey="name"
            />

            {routeRules.length > 0 && (
              <div style={{ marginTop: 16, textAlign: 'right' }}>
                <Pagination
                  current={pagination.current}
                  pageSize={pagination.pageSize}
                  total={pagination.total}
                  onChange={(page, pageSize) => fetchRouteRules(page, pageSize)}
                  showSizeChanger
                  showQuickJumper
                  showTotal={(total, range) =>
                    `共 ${total} 条记录，第 ${range[0]}-${range[1]} 条`
                  }
                />
              </div>
            )}
          </div>
        )}
      </div>

      {/* 添加路由规则二级抽屉 */}
      <AddRouteRuleDrawer
        visible={addRuleDrawerVisible}
        instanceId={pluginData?.instanceId || ''}
        onClose={() => setAddRuleDrawerVisible(false)}
        onSuccess={handleAddRuleSuccess}
      />

      {/* 编辑路由规则二级抽屉 */}
      <EditRouteRuleDrawer
        visible={editRuleDrawerVisible}
        instanceId={pluginData?.instanceId || ''}
        ruleName={editingRuleName}
        onClose={() => {
          setEditRuleDrawerVisible(false);
          setEditingRuleName('');
        }}
        onSuccess={() => {
          setEditRuleDrawerVisible(false);
          setEditingRuleName('');
          if (activeTab === 'route-level') {
            fetchRouteRules(pagination.current, pagination.pageSize);
          }
        }}
      />
    </Drawer>
  );
};

export default PluginConfigDrawer;
