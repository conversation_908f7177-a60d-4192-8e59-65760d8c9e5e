import React, { useState } from 'react';
import { Tabs, toast } from 'acud';
import PluginMarket from './components/PluginMarket';
import InstalledPlugins from './components/InstalledPlugins';
import InstallPluginModal from './components/InstallPluginModal';
import PluginConfigDrawer from './components/PluginConfigDrawer';
import styles from './index.module.less';

const { TabPane } = Tabs;

interface PluginManagementProps {
  detail?: any;
  requestDetail?: () => void;
  instanceId: string;
}

const PluginManagement: React.FC<PluginManagementProps> = ({
  detail,
  requestDetail,
  instanceId
}) => {
  console.log('PluginManagement组件加载，instanceId:', instanceId);
  
  const [activeTab, setActiveTab] = useState('plugin-market');
  const [installModalVisible, setInstallModalVisible] = useState(false);
  const [selectedPlugin, setSelectedPlugin] = useState<any>(null);
  const [refreshKey, setRefreshKey] = useState(0);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [drawerPluginData, setDrawerPluginData] = useState<any>(null);

  // 处理标签页切换
  const handleTabChange = (key: string) => {
    console.log('切换标签页:', key);
    setActiveTab(key);
  };



  // 打开安装插件弹窗
  const openInstallModal = (plugin: any) => {
    console.log('打开安装插件弹窗，插件信息:', plugin);
    setSelectedPlugin(plugin);
    setInstallModalVisible(true);
  };

  // 关闭安装插件弹窗
  const closeInstallModal = () => {
    setInstallModalVisible(false);
    setSelectedPlugin(null);
  };

  // 安装成功回调
  const handleInstallSuccess = () => {
    console.log('插件安装成功，刷新页面数据');
    setRefreshKey(prev => prev + 1);
    closeInstallModal();
    toast.success({
      message: '插件安装成功',
      duration: 3
    });
  };

  // 打开配置抽屉
  const openConfigDrawer = (pluginData: any) => {
    console.log('打开配置抽屉，插件数据:', pluginData);
    setDrawerPluginData(pluginData);
    setDrawerVisible(true);
  };

  // 关闭配置抽屉
  const closeConfigDrawer = () => {
    setDrawerVisible(false);
    setDrawerPluginData(null);
  };

  // 处理前往配置
  const handleConfigure = (plugin: any) => {
    console.log('前往配置插件:', plugin);
    // 对于已安装的插件，直接打开配置抽屉
    openConfigDrawer({
      pluginName: plugin.pluginName,
      pluginId: `installed-${plugin.pluginName}`, // 已安装插件的ID
      instanceId: instanceId,
      // 使用默认配置数据
      defaultConfig: {
        enabled: true,
        name: `${plugin.pluginName}-config`,
        description: plugin.description || '外部认证插件',
        scope: 'gateway',
        httpService: 
`endpoint_mode: envoy
endpoint:
  service_name: ext-auth.backend.svc.cluster.local
  service_port: 8090
  path_prefix: /auth
timeout: 1000`,
        statusOnError: 403
      }
    });
  };

  return (
    <div className={styles['plugin-management']}>
        <Tabs
          activeKey={activeTab}
          onChange={handleTabChange}
          className={styles['plugin-tabs']}
        >
          <TabPane tab="插件市场" key="plugin-market" />
          <TabPane tab="已安装插件" key="installed-plugins" />
        </Tabs>

      <div className={styles['plugin-content']}>
        {activeTab === 'plugin-market' && (
          <PluginMarket
            instanceId={instanceId}
            onInstall={openInstallModal}
            onConfigure={handleConfigure}
            refreshKey={refreshKey}
          />
        )}
        
        {activeTab === 'installed-plugins' && (
          <InstalledPlugins
            instanceId={instanceId}
            refreshKey={refreshKey}
            onConfigure={handleConfigure}
            onInstallPlugin={() => setActiveTab('plugin-market')}
          />
        )}
      </div>

      {/* 安装插件弹窗 */}
      <InstallPluginModal
        visible={installModalVisible}
        plugin={selectedPlugin}
        instanceId={instanceId}
        onCancel={closeInstallModal}
        onSuccess={handleInstallSuccess}
        onOpenDrawer={openConfigDrawer}
      />

      {/* 插件配置抽屉 */}
      <PluginConfigDrawer
        visible={drawerVisible}
        pluginData={drawerPluginData}
        onClose={closeConfigDrawer}
      />
    </div>
  );
};

export default PluginManagement;
