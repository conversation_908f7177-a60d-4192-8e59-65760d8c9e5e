.plugin-management {
  padding: 0;
  
  .plugin-management-header {
    margin-bottom: 24px;

    .plugin-tabs {
      :global(.acud-tabs-nav) {
        margin-bottom: 0;
        border-bottom: 1px solid #E8E9EB;
      }

      :global(.acud-tabs-tab) {
        padding: 11px 0;
        margin-right: 32px;
        font-size: 14px;
        color: #151B26;

        &:global(.acud-tabs-tab-active) {
          color: #2468F2;
          border-bottom-color: #2468F2;
        }
      }
    }
  }
  
  .plugin-content {
    min-height: 400px;
  }
}

// 插件市场样式
.plugin-market {
  .plugin-cards {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-top: 24px;
  }

  .plugin-card {
    width: 310px;
    background: #FFFFFF;
    border: 1px solid #E8E9EB;
    border-radius: 6px;
    padding: 16px;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    gap: 24px;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 16px;

      .title-section {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;

        .plugin-title {
          font-family: 'PingFang SC';
          font-size: 12px;
          font-weight: 500;
          color: #151B26;
          line-height: 20px;
          margin: 0;
        }

        .installed-status {
          display: flex;
          align-items: center;
          gap: 6px;

          .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #30BF13;
          }

          .status-text {
            font-size: 12px;
            color: #151B26;
            line-height: 20px;
          }
        }
      }

      .plugin-tag {
        flex-shrink: 0;

        :global(.acud-tag) {
          background: #FFF4E6;
          color: #FF9326;
          border: none;
          font-size: 12px;
          line-height: 20px;
          padding: 0 8px;
          height: 20px;
          display: flex;
          align-items: center;
          border-radius: 2px;
        }
      }
    }

    .plugin-description {
      font-family: 'PingFang SC';
      font-size: 12px;
      color: #5C5F66;
      line-height: 22px;
      margin: 0;
      flex: 1;
    }

    .card-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 12px;

      .plugin-intro {
        font-family: 'PingFang SC';
        font-size: 12px;
        color: #2468F2;
        line-height: 17px;
        margin: 0;
      }

      .install-action {
        font-family: 'PingFang SC';
        font-size: 12px;
        color: #2468F2;
        cursor: pointer;
        line-height: 20px;
        margin: 0;

        &:hover {
          color: #1c5dd9;
        }
      }
    }
  }
}

// 已安装插件样式
.installed-plugins {
  .plugins-table {
    margin-top: 24px;
    
    :global(.acud-table) {
      background: #FFFFFF;
      border-radius: 6px;
    }
    
    :global(.acud-table-thead > tr > th) {
      background: #F7F8FA;
      border-bottom: 1px solid #E8E9EB;
      font-weight: 500;
      color: #151B26;
    }
    
    :global(.acud-table-tbody > tr > td) {
      border-bottom: 1px solid #F0F1F5;
    }
    
    .status-enabled {
      color: #30BF13;
    }
    
    .status-disabled {
      color: #84868C;
    }
    
   
  }
}

// 空状态样式
.empty-state {
  text-align: center;
  padding: 60px 0;
  color: #84868C;
  font-size: 14px;
}

// 安装插件弹窗样式
.install-modal {
  .field-item {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    .field-label {
      font-size: 14px;
      color: #5C5F66;
      margin-bottom: 8px;
      font-weight: 400;
      line-height: 20px;
    }

    .field-value {
      font-size: 14px;
      color: #151B26;
      line-height: 22px;
      padding: 8px 12px;
      background-color: #F7F8FA;
      border-radius: 6px;
      border: 1px solid #E8E9EB;

      &.multi-line {
        min-height: 66px;
      }
    }
  }
}

// 插件配置抽屉样式
.plugin-config-drawer {
  .drawer-content {
    // padding: 24px;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .drawer-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;

    :global(.acud-btn) {
      height: 32px;
      padding: 6px 12px;
      font-size: 12px;
      border-radius: 4px;

      &:global(.acud-btn-primary) {
        background: #2468F2;
        border-color: #2468F2;
        color: #FFFFFF;
      }

      &:global(.acud-btn-default) {
        background: #FFFFFF;
        border-color: #E8E9EB;
        color: #151B26;
      }
    }
  }

  .tab-buttons {
    display: flex;
    gap: 12px;

    :global(.acud-btn) {
      height: 32px;
      padding: 6px 12px;
      font-size: 12px;
      border-radius: 4px;

      &:global(.acud-btn-primary) {
        background: #2468F2;
        border-color: #2468F2;
        color: #FFFFFF;
      }

      &:global(.acud-btn-default) {
        background: #FFFFFF;
        border-color: #E8E9EB;
        color: #151B26;
      }
    }
  }

  .gateway-config {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 24px;

    .config-item {
      display: flex;
      align-items: flex-start;
      gap: 16px;

      .config-label {
        font-size: 12px;
        color: #000000;
        line-height: 20px;
        min-width: 60px;
        margin-top: 0;
      }

      .config-value {
        font-size: 12px;
        color: #000000;
        line-height: 20px;
      }

      .code-editor-container {
        flex: 1;

        .editor-actions {
          margin-top: 12px;

          :global(.acud-btn-primary) {
            background: #2468F2;
            border-color: #2468F2;
            color: #FFFFFF;
            font-size: 12px;
            height: 32px;
            padding: 6px 12px;
          }
        }
      }
    }
  }

  .route-config {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 24px;

    .route-actions {
      display: flex;
      justify-content: flex-start;

      :global(.acud-btn-primary) {
        background: #2468F2;
        border-color: #2468F2;
        color: #F7F7F9;
        font-size: 12px;
        height: 32px;
        padding: 6px 12px;
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }

    .route-table {
      flex: 1;

      :global(.acud-table) {
        background: #FFFFFF;
        border-radius: 6px;
      }

      :global(.acud-table-thead > tr > th) {
        background: #F7F8FA;
        border-bottom: 1px solid #E8E9EB;
        font-weight: 500;
        color: #151B26;
        font-size: 12px;
      }

      :global(.acud-table-tbody > tr > td) {
        border-bottom: 1px solid #F0F1F5;
        font-size: 12px;
      }
    }
  }
}

// 已安装插件表格样式
.rule-count-cell {
  display: flex;
  flex-direction: column;
  gap: 2px;

  .rule-count-text {
    font-size: 12px;
    color: #151B26;
    line-height: 20px;
  }

  .ant-line {
    width: 24px;
    height: 1px;
    border-bottom: 1px dashed #5C5F66;
  }
}

.action-buttons {
  display: flex;
  gap: 8px;

  :global(.acud-btn) {
    padding: 0;
    height: auto;
    line-height: 1;
    font-size: 12px;

    &:global(.acud-btn-text) {
      color: #2468F2;

      &:hover {
        color: #1A5CE6;
      }

      &:global(.acud-btn-dangerous) {
        color: #F5222D;

        &:hover {
          color: #CF1322;
        }
      }
    }
  }
}
