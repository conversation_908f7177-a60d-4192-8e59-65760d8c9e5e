# 插件管理功能

## 功能概述

插件管理功能为AI网关实例提供了插件的安装、卸载和管理能力。目前支持外部认证插件的管理。

## 功能特性

### 插件市场
- 展示所有可用插件列表（包括已安装和未安装的插件）
- 卡片式布局，严格按照设计稿要求实现
- 插件信息包括：标题、分类标签、描述、操作按钮
- **未安装插件**：显示"安装"按钮，点击弹出安装配置弹窗
- **已安装插件**：
  - 插件标题、已安装状态、分类标签在同一行显示
  - 已安装状态包含绿色圆点和"已安装"文字
  - 显示"前往配置"按钮，点击直接打开配置抽屉
- 根据插件安装状态动态更新UI显示
- 插件描述：实现了向外部授权服务发送鉴权请求，以检查客户端请求是否得到授权

### 已安装插件
- 表格形式展示已安装的插件，包含4列：
  1. **插件名称** - 显示插件的显示名称
  2. **规则数量** - 显示格式为"X条"，下方有蚂蚁线样式
  3. **插件类型** - 显示插件的分类类型（如：认证鉴权）
  4. **操作** - 包含"规则配置"和"卸载"两个按钮
- **页面操作按钮**：
  - **安装插件**按钮：位于页面左侧，点击跳转到插件市场标签页
  - **刷新**按钮：位于页面右侧，点击刷新当前已安装插件列表
- **规则数量列特性**：
  - 鼠标悬停显示气泡提示，展示具体规则详情
  - 蚂蚁线样式：虚线下划线效果
  - 提示内容包含路由级和网关级规则统计
- 使用acud组件默认样式，移除多余的自定义样式
- 支持分页显示
- 二次确认卸载操作（使用Popconfirm组件）

### 安装插件弹窗
- 采用静态文本显示方式，移除了表单输入框
- 水平布局：字段标签和值在同一行显示
- 显示字段：插件名称、插件描述、网关实例ID（均为静态文本）
- 样式符合设计规范：灰色背景、圆角边框、合适的内边距
- 点击"安装并配置"调用创建外部认证插件接口
- 使用默认配置进行安装
- 安装成功后自动打开规则配置抽屉

### 规则配置抽屉
- 抽屉标题：规则配置（外部认证插件）
- 顶部两个切换按钮：网关级别、路由级别（默认选中网关级别）
- **网关级别配置**：
  - 启用状态：开关组件控制
  - 生效范围：显示"网关级别"文本
  - 插件规则：代码编辑器，支持YAML语法高亮和行号显示
  - 代码编辑器左下方有保存按钮（用于临时保存配置）
- **路由级别配置**：
  - 顶部"添加规则"按钮（带加号图标）
  - 规则列表表格，包含6列：规则名称、生效范围、匹配类型、路由列表、启用状态、操作
  - 支持分页显示
  - 操作列包含编辑和删除按钮
- **底部操作按钮**：
  - **显示条件**：仅在选择"网关级别"标签页时显示
  - **取消按钮**：关闭抽屉，不保存任何更改
  - **确定按钮**：调用创建外部认证插件接口，创建网关级别插件规则
  - **路由级别**：不显示底部按钮，用户通过"添加规则"按钮管理路由规则

## 技术实现

### 组件结构
```
PluginManagement/
├── index.tsx                    # 主组件
├── index.module.less           # 样式文件
├── components/
│   ├── PluginMarket.tsx        # 插件市场组件
│   ├── InstalledPlugins.tsx    # 已安装插件组件
│   ├── InstallPluginModal.tsx  # 安装插件弹窗
│   ├── PluginConfigDrawer.tsx  # 插件配置抽屉
│   └── CodeEditor.tsx          # 代码编辑器组件
└── README.md                   # 说明文档
```

### API接口
- `getPluginMarketList`: 查询插件市场插件列表
- `uninstallPlugin`: 卸载插件
- `createExternalAuthPlugin`: 创建外部认证插件
- `getExternalAuthPluginList`: 查询外部认证插件列表（更新为使用extAuthList接口）
  - 支持scope参数过滤（route/gateway）
  - 支持name参数模糊匹配
  - 支持分页和排序功能
  - 响应数据结构：`response.page.result`包含规则列表
  - 字段映射：name、enabled、scope、matchType、routeList、httpService、updateTime
  - 在标签页切换到路由级别时自动调用
  - 在组件初始化时根据activeTab状态调用
- `getExternalAuthPluginDetail`: 查询外部认证插件详情
- `updateExternalAuthPlugin`: 更新外部认证插件（支持启用/停用操作）
- `deleteExternalAuthPlugin`: 删除外部认证插件
- `getRouteList`: 查询路由列表（含外部认证状态）
  - 接口路径：`/api/aigw/v1/aigateway/{instanceId}/routes`
  - 返回路由名称和是否关联外部认证插件的状态
  - 用于Transfer组件的数据源
- `getExternalAuthPluginDetail`: 查询外部认证插件详情
  - 接口路径：`/api/aigw/v1/aigateway/{instanceId}/extAuth/{ruleName}`
  - 返回插件的完整配置信息，包括enabled、routeList、httpService等
  - 用于编辑规则时的数据预填充

### 核心组件功能
- **PluginConfigDrawer**: 插件配置抽屉，支持网关级别和路由级别配置切换
  - 网关级别：显示底部确定/取消按钮，支持创建网关级别插件规则
  - 路由级别：6列表格结构，移除自定义样式，使用acud组件默认样式
    - 规则名称（140px）、目标路由（160px）、生效状态（110px）、规则内容（200px）、更新时间（150px）、操作（180px）
    - **操作列优化**：使用`<a>`标签实现，包含编辑、启用/停用、删除三个操作，所有操作使用统一的蓝色样式
    - **规则内容列优化**：支持内容截断、气泡提示完整内容、一键复制功能
    - 根据生效状态显示不同操作按钮（已启用：编辑、停用、删除；未启用：编辑、启用、删除）
    - 支持"添加规则"功能，点击打开二级抽屉
  - 切换按钮使用enhance类型而非primary类型
- **AddRouteRuleDrawer**: 添加路由规则二级抽屉组件
  - 嵌套在主插件配置抽屉内的二级抽屉
  - 5个表单字段：启用状态、生效范围、规则名称、目标路由、插件规则
  - 目标路由使用Transfer穿梭框组件，支持多选和搜索
  - 插件规则使用CodeEditor组件，支持YAML语法高亮
  - 成功创建后刷新路由级别规则列表
- **EditRouteRuleDrawer**: 编辑路由规则二级抽屉组件
  - 嵌套在主插件配置抽屉内的二级抽屉
  - 数据预填充：调用详情接口获取当前规则的完整配置信息
  - 5个表单字段：启用状态（可编辑）、生效范围（固定显示）、规则名称（只读显示）、目标路由（可编辑）、插件规则（可编辑）
  - 目标路由预填充已选路由，支持重新选择
  - 插件规则预填充当前YAML配置，支持修改
  - 成功更新后刷新路由级别规则列表
- **CodeEditor**: 自定义代码编辑器，支持YAML语法高亮、行号显示、Tab缩进
- **InstallPluginModal**: 安装插件弹窗，点击后直接打开配置抽屉
- **InstalledPlugins**: 已安装插件表格组件
  - 5列表格结构：插件名称、规则数量、插件类型、安装时间、操作
  - 规则数量列支持气泡提示和蚂蚁线样式
  - 操作列包含规则配置和卸载功能

### 路由配置
插件管理页面已集成到实例详情页面的左侧导航菜单中，菜单项为"插件管理"。

## 使用说明

1. 在实例详情页面点击左侧"插件管理"菜单项
2. 默认显示"插件市场"标签页，展示所有可用插件（包括已安装和未安装的）
3. **对于未安装的插件**：点击"安装"按钮，弹出安装配置弹窗
4. **对于已安装的插件**：点击"前往配置"按钮，直接打开配置抽屉
5. 弹窗中显示插件名称、描述和实例ID（均为只读）
6. 点击"安装并配置"按钮，直接打开规则配置抽屉（不调用安装接口）
7. 在抽屉中进行插件配置：
   - **网关级别**：配置启用状态、查看生效范围、编辑插件规则
     - 可以使用"保存"按钮临时保存配置更改
     - 配置完成后点击右下角"确定"按钮正式创建网关级别插件
     - 点击"取消"按钮可以关闭抽屉而不保存任何更改
   - **路由级别**：查看和管理路由规则列表
     - 不显示底部的确定/取消按钮
     - 点击"添加规则"按钮打开二级抽屉
     - 在二级抽屉中配置：启用状态、规则名称、目标路由（Transfer组件）、插件规则（YAML编辑器）
     - 目标路由支持多选和搜索，已关联外部认证的路由不可选
     - 配置完成后点击"确定"创建路由级别规则
     - 点击表格中的"编辑"操作打开编辑二级抽屉
     - 编辑抽屉自动预填充当前规则的配置信息
     - 规则名称为只读显示，其他字段可编辑
     - 修改完成后点击"更新"保存更改
8. 切换到"已安装插件"标签页查看已安装的插件
9. 点击"卸载"按钮可以卸载插件（需要二次确认）

## 设计稿实现

- 插件卡片严格按照设计稿实现：310px宽度，16px内边距，24px间距
- 标签使用橙色样式（#FFF4E6背景，#FF9326文字）
- 字体、颜色、间距完全符合设计稿要求
- 移除了刷新按钮，简化界面

## 注意事项

1. 目前仅支持外部认证插件（ext-auth）
2. 点击"安装并配置"后直接打开配置抽屉，不会调用实际的安装接口
3. 配置抽屉中的操作说明：
   - **网关级别**：
     - 使用默认的YAML配置
     - "保存"按钮为模拟保存，仅用于临时保存配置更改
     - "确定"按钮调用真实的创建外部认证插件接口，固定创建网关级别插件
     - 接口参数严格按照文档规范：scope固定为"gateway"
   - **路由级别**：
     - 显示模拟的路由规则列表
     - 不显示底部确定/取消按钮
     - 通过表格中的操作按钮管理路由规则
4. 卸载插件会删除该插件的所有配置规则
5. 所有操作都会有相应的toast提示信息，严格按照acud组件规范使用对象格式：
   ```javascript
   toast.success({
     message: '操作成功',
     duration: 3
   });
   ```
6. 插件市场显示所有插件，根据安装状态提供不同的操作方式：
   - 未安装：显示"安装"按钮
   - 已安装：显示"已安装"状态和"前往配置"按钮

## 接口数据修复说明

### 路由级别规则列表接口修复
1. **接口文档更新**：补充了缺失的字段定义
   - 添加了`routeList`字段（array[string]类型）
   - 完善了`matchType`和`scope`字段的描述
   - 更新了响应示例，包含完整的字段信息

2. **前端代码修复**：
   - 更新了`RouteRule`类型定义，添加`routeList?`字段
   - 修复了目标路由列的显示逻辑，正确处理`routeList`数组
   - 路由级别规则显示：`record.routeList.join('、')`
   - 网关级别规则显示：`'全局路由'`

3. **数据字段映射**：
   - `name` → 规则名称
   - `routeList` → 目标路由（数组格式，用顿号分隔显示）
   - `enabled` → 生效状态（带圆点指示器）
   - `httpService` → 规则内容概要（提取服务名称和端口）
   - `updateTime` → 更新时间（直接使用接口返回格式，不做转换）

## 时间格式处理优化

### 修改说明
- **原有处理**：使用`toLocaleString`格式化时间为年/月/日格式
- **优化后处理**：直接使用接口返回的时间格式，不做任何转换
- **影响范围**：
  - 已安装插件表格的"安装时间"列
  - 路由级别规则表格的"更新时间"列
  - 插件配置抽屉中的所有时间显示

### 实现方式
```javascript
// 修改前
const formatTime = (timeStr: string) => {
  const date = new Date(timeStr);
  return date.toLocaleString('zh-CN', {...});
};

// 修改后
const formatTime = (timeStr: string) => {
  return timeStr || '-';
};
```

## 错误修复说明

### 编辑规则undefined错误修复
修复了点击编辑按钮时出现的"Cannot read properties of undefined (reading 'split')"错误：

1. **CodeEditor组件修复**：
   ```javascript
   // 修复前
   {value.split('\n').map((_, index) => (...))}

   // 修复后
   {(value || '').split('\n').map((_, index) => (...))}
   ```

2. **数据预填充安全处理**：
   ```javascript
   // 表单字段默认值处理
   form.setFieldsValue({
     enabled: detail.enabled || false,
     name: detail.name || '',
     description: detail.description || '',
     httpService: detail.httpService || ''
   });

   // 路由列表数据过滤
   const validRoutes = detail.routeList.filter(route => route && typeof route === 'string');
   ```

3. **Transfer组件数据安全处理**：
   - 路由数据映射时添加默认值
   - 提交时过滤无效的路由键值
   - 防止undefined值导致的渲染错误

### 已安装插件页面优化
移除了"安装时间"列及其相关代码逻辑：

1. **删除的内容**：
   - 删除了`formatTime`函数
   - 删除了表格列定义中的"安装时间"列
   - 简化了Popconfirm组件的属性（移除description，合并到title中）

2. **表格结构优化**：
   - 从5列减少到4列：插件名称、规则数量、插件类型、操作
   - 提高了表格的简洁性和可读性
   - 减少了不必要的时间信息显示

3. **空状态优化**：
   - 参考访问控制页面的空状态样式
   - 空状态文案：`暂无已安装插件。安装插件`
   - 提供快捷操作：点击"安装插件"链接直接跳转到插件市场
   - 使用OutlinedPlusNew图标，与操作按钮保持一致

## 关键问题修复

### 问题1：外部认证插件列表展示异常
**修复内容**：
1. **数据源修复**：
   - 原问题：只调用`getPluginMarketList`接口，无法获取实际的外部认证插件规则列表
   - 修复方案：同时调用`getPluginMarketList`和`getExternalAuthPluginList`接口
   - 数据合并：使用插件市场数据确定安装状态，使用外部认证插件列表计算准确的规则统计

2. **停用规则显示修复**：
   - 原问题：停用路由级别规则后，页面数据完全消失
   - 修复方案：确保获取所有规则（包括已停用的），通过`enabled`字段区分状态
   - 实现：调用`getExternalAuthPluginList`时不过滤`enabled`状态，保留所有规则

3. **API响应处理修复**：
   - 原问题：直接访问`response.success`和`response.page`导致类型错误
   - 修复方案：统一处理API响应数据结构：`(response as any)?.data || response`
   - 影响范围：所有API调用的响应处理逻辑

### 问题2：规则数量统计错误
**修复内容**：
1. **统计逻辑重构**：
   - 原问题：依赖插件市场接口返回的不准确统计数据
   - 修复方案：基于实际的外部认证插件规则列表重新计算统计数据
   - 实现：遍历`extAuthResponse.page.result`，按`scope`字段分类统计

2. **启用规则数量修复**：
   - 原问题：气泡提示中的启用规则数量显示错误
   - 修复方案：分别统计网关级别和路由级别的启用规则数量
   - 网关级别：统计`scope === 'gateway' && enabled === true`的规则数量
   - 路由级别：统计`scope === 'route' && enabled === true`的规则数量

3. **气泡提示优化**：
   - 修复前：网关级规则显示为"已启用/未启用"的布尔值
   - 修复后：显示具体的启用规则数量，格式统一为"X 条已启用"

### 修复代码示例
```javascript
// 修复后的规则统计逻辑
const enrichedPlugins = installedPlugins.map((plugin: InstalledPlugin) => {
  let actualRuleCount = { total: 0, routeRuleCount: 0, gatewayRuleCount: 0 };
  let routeEnabledCount = 0;
  let gatewayEnabledCount = 0;

  if (extAuthData?.success && extAuthData?.page?.result) {
    const rules = extAuthData.page.result;

    // 统计网关级别规则
    const gatewayRules = rules.filter((rule: any) => rule.scope === 'gateway');
    actualRuleCount.gatewayRuleCount = gatewayRules.length;
    gatewayEnabledCount = gatewayRules.filter((rule: any) => rule.enabled).length;

    // 统计路由级别规则
    const routeRules = rules.filter((rule: any) => rule.scope === 'route');
    actualRuleCount.routeRuleCount = routeRules.length;
    routeEnabledCount = routeRules.filter((rule: any) => rule.enabled).length;

    actualRuleCount.total = actualRuleCount.gatewayRuleCount + actualRuleCount.routeRuleCount;
  }

  return {
    ...plugin,
    ruleCount: actualRuleCount,
    routeEnabledCount,
    gatewayEnabledCount
  };
});
```

### 修复验证
1. **网关级别和路由级别规则**：现在都能正确显示在列表中
2. **停用规则保留**：停用规则后仍然显示在列表中，状态正确标识
3. **规则数量准确**：基于实际规则列表计算，数量统计准确
4. **气泡提示正确**：启用规则数量显示格式统一且准确

## 扩展性

该插件管理系统设计时考虑了扩展性：
- 可以轻松添加新的插件类型
- 插件卡片和表格列可以根据插件类型动态调整
- API接口设计支持多种插件类型的管理
