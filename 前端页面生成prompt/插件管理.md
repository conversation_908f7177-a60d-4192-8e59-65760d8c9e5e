# 插件管理页面

## 1. 现状描述
目前在实例详情页面中左侧有多个导航栏（实例详情、推理服务、路由配置、消费者管理、访问控制、监控与告警），点击每个导航栏右侧会切换成当前导航栏下对应的页面。

## 2. 需求描述
现在，我期望在这些导航栏中新增一个导航栏【插件管理】，用于展示当前实例中的插件市场和已安装插件管理功能。接下来在第3点页面交互中我会描述选中插件管理这个导航栏后右侧页面的样式和交互，你需要在遵循相关规范的前提下生成这个页面。另外，这个页面中所有字段和字段值来源于相关插件接口，接口文档请参考插件相关接口文档，你需要按照接口文档中对应的字段名、请求格式、请求参数、响应参数生成这个页面并和接口对应上。

## 3. 页面交互与样式
插件管理页面包含两个标签页：**插件市场** 和 **已安装插件**，默认选中插件市场标签页。

### 3.1 插件市场标签页
1. **页面布局**：插件市场标签页中展示可安装的插件列表，采用卡片式布局展示
2. **插件卡片**：每个插件以卡片形式展示，包含以下信息：
   - 插件名称（如：外部认证）
   - 插件标签（如：认证鉴权，橙色标签）
   - 插件描述（如：实现了向外部授权服务发送鉴权请求，以检查客户端请求是否得到授权）
   - 插件介绍标识
   - 安装按钮（蓝色文字链接样式）
3. **卡片样式**：
   - 白色背景，灰色边框
   - 圆角边框
   - 内边距16px
   - 卡片间距合理排列
4. **交互逻辑**：
   - 点击"安装"按钮后，在当前页面出现弹窗，标题为安装插件，内容为安装插件的表单，包含以下字段：
     - 插件名称（不可编辑，显示插件名称）、插件描述（不可编辑，显示插件描述）、网关实例ID（不可编辑，显示当前实例ID）
     弹窗右下角为【安装并配置】和【取消】按钮，点击安装并配置后调用 安装插件接口，
   - 安装成功后toast提示"插件安装成功"
   - 安装失败则toast提示具体错误信息

### 3.2 已安装插件标签页
1. **页面布局**：已安装插件标签页中展示已安装的插件列表，采用表格形式展示
2. **表格列**：包含以下列
   - 插件名称
   - 插件描述
   - 安装时间
   - 状态（启用/禁用）
   - 操作（启用/禁用、卸载）
3. **表格功能**：
   - 支持分页，默认10条/页
   - 支持刷新功能
4. **操作功能**：
   - **启用/禁用**：点击后调用启用/禁用插件接口，成功后更新状态并toast提示
   - **卸载**：点击后弹出二次确认弹窗，确认后调用卸载插件接口，成功后从列表中移除该插件并toast提示"插件卸载成功"

## 4. 页面与接口对应关系

### 4.1 插件市场标签页
1. **页面加载**：进入插件市场标签页时，调用【查询可安装插件列表】接口，获取所有可安装的插件信息
2. **安装插件**：点击插件卡片中的"安装"按钮时，调用【安装插件】接口，传入插件ID和实例ID
3. **安装成功处理**：安装成功后重新调用【查询可安装插件列表】和【查询已安装插件列表】接口刷新两个标签页数据

### 4.2 已安装插件标签页
1. **页面加载**：进入已安装插件标签页时，调用【查询插件市场插件】接口，获取所有已安装的插件信息

### 4.3 实例ID获取
所有接口调用都需要实例ID参数，可以从当前路由路径中获取实例ID。

## 5. 可维护性
尽量不要修改存量代码，如果实在需要修改请写上注释并保留原代码注释掉即可。另外，在你认为比较重要的操作节点时使用console.log输出关键信息以方便后期前后端联调。

## 6. 接口文档
参见接口文档文件夹中的插件相关接口文档：
- 查询可安装插件列表接口
- 查询已安装插件列表接口  
- 安装插件接口
- 卸载插件接口
- 启用插件接口
- 禁用插件接口

## 7. 遵守原则
尽量不要自动生成我没有提到的交互逻辑，比如在本次prompt中我只希望你生成插件管理这个页面的标签页【插件市场】功能，所以你不应该再自动为我完善其他的交互页面。所有字段以接口文档为准，严格按照接口文档中的字段名称和数据结构进行开发。

## 8. 特殊说明
1. 插件卡片的样式需要严格按照设计稿中的样式实现，包括标签颜色、字体大小、间距等
2. 二次确认弹窗使用DialogBox组件而不是Modal组件
3. 状态切换（启用/禁用）需要实时反馈，操作成功后立即更新UI状态
4. 所有toast提示需要明确指出操作结果和插件名称
